import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import os
from ultralytics import YOLO

class TireDetector:
    def __init__(self, root):
        """初始化轮胎检测器GUI界面"""
        self.root = root
        self.root.title("轮胎检测")
        self.root.geometry("1300x700")  # 增加窗口宽度以容纳新的显示区域
        
        # 存储当前图像和检测状态
        self.current_image = None
        self.result_image = None
        self.tire_boxes = []  # 存储检测到的轮胎边界框
        self.processed_tire_images = []  # 存储处理后的轮胎图像
        
        # 调试步骤控制
        self.current_tire_index = 0  # 当前处理的轮胎索引
        self.current_step = 0  # 当前处理步骤
        self.tire_processing_data = []  # 存储轮胎处理的中间数据
        
        # YOLO模型相关
        self.model = None
        self.model_path = ""
        self.detection_results = None
        
        # 定义参数变量
        self.gaussian_kernel_size = tk.IntVar(value=5)
        self.canny_threshold1 = tk.IntVar(value=50)
        self.canny_threshold2 = tk.IntVar(value=150)
        self.morph_kernel_size = tk.IntVar(value=5)  # 形态学核大小参数
        
        # 创建界面
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域 - 减少内边距
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=(10, 5))
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 模型和图像选择区域 - 放在同一行
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=2)
        
        # 模型选择
        ttk.Label(file_frame, text="YOLO模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.model_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.model_path_var, width=30).grid(row=0, column=1, padx=2, pady=2, sticky=tk.EW)
        ttk.Button(file_frame, text="选择模型", command=self.select_model).grid(row=0, column=2, padx=2, pady=2)
        
        # 图像选择
        ttk.Label(file_frame, text="图像文件:").grid(row=0, column=3, sticky=tk.W, padx=(10, 5), pady=2)
        self.image_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.image_path_var, width=30).grid(row=0, column=4, padx=2, pady=2, sticky=tk.EW)
        ttk.Button(file_frame, text="选择图像", command=self.select_image).grid(row=0, column=5, padx=2, pady=2)
        
        # 配置列权重
        file_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(4, weight=1)
        
        # 添加参数调整区域 - 使用更紧凑的布局
        param_frame = ttk.LabelFrame(control_frame, text="参数调整", padding=(5, 2))
        param_frame.pack(fill=tk.X, pady=2)
        
        # 创建更紧凑的网格布局
        # 第一行包含所有参数
        ttk.Label(param_frame, text="Canny阈值1:").grid(row=0, column=0, padx=5, pady=2, sticky=tk.W)
        ttk.Entry(param_frame, textvariable=self.canny_threshold1, width=5).grid(row=0, column=1, padx=2, pady=2, sticky=tk.W)
        
        ttk.Label(param_frame, text="Canny阈值2:").grid(row=0, column=2, padx=5, pady=2, sticky=tk.W)
        ttk.Entry(param_frame, textvariable=self.canny_threshold2, width=5).grid(row=0, column=3, padx=2, pady=2, sticky=tk.W)
        
        ttk.Label(param_frame, text="形态学核大小:").grid(row=0, column=4, padx=5, pady=2, sticky=tk.W)
        morph_size_values = [3, 5, 7, 9, 11, 13, 15]
        ttk.Combobox(param_frame, textvariable=self.morph_kernel_size, values=morph_size_values, width=5, state="readonly").grid(row=0, column=5, padx=2, pady=2, sticky=tk.W)
        
        # 按钮放在同一行的右侧
        ttk.Button(param_frame, text="更新参数", command=self.update_parameters).grid(row=0, column=6, padx=(15, 5), pady=2)
        self.reprocess_button = ttk.Button(param_frame, text="重新处理", command=self.reprocess_current_tire, state=tk.DISABLED)
        self.reprocess_button.grid(row=0, column=7, padx=5, pady=2)
        
        # 为param_frame配置列权重，使其均匀分布
        for i in range(8):
            param_frame.columnconfigure(i, weight=1)
        
        # 删除以下代码块，因为它使用了pack布局，与上面的grid布局冲突
        # 创建按钮行
        # button_row = ttk.Frame(param_frame)
        # button_row.pack(fill=tk.X, pady=5)
        
        # 添加更新参数按钮和重新处理按钮
        # ttk.Button(button_row, text="更新参数", command=self.update_parameters).pack(side=tk.LEFT, padx=10)
        # self.reprocess_button = ttk.Button(button_row, text="重新处理", command=self.reprocess_current_tire, state=tk.DISABLED)
        # self.reprocess_button.pack(side=tk.LEFT, padx=10)
        
        # 处理按钮 - 更紧凑的布局
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=2)
        
        # 添加检测按钮
        self.detect_button = ttk.Button(button_frame, text="检测目标", command=self.detect_objects)
        self.detect_button.pack(side=tk.LEFT, padx=5)
        
        # 添加轮胎图像处理按钮
        self.process_tire_button = ttk.Button(button_frame, text="轮胎图像处理", command=self.process_tire_images)
        self.process_tire_button.pack(side=tk.LEFT, padx=5)
        
        # 添加调试步骤控制按钮
        self.debug_next_button = ttk.Button(button_frame, text="下一步处理", command=self.process_next_step, state=tk.DISABLED)
        self.debug_next_button.pack(side=tk.LEFT, padx=5)
        
        # 添加保存结果按钮
        self.save_button = ttk.Button(button_frame, text="保存结果", command=self.save_result)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示 - 减少内边距
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(control_frame, textvariable=self.status_var).pack(anchor=tk.W, pady=2)
        
        # 创建图像显示区域框架
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧原始图像显示区域
        left_display_frame = ttk.LabelFrame(display_frame, text="原始图像显示")
        left_display_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 图像画布
        self.image_canvas = tk.Canvas(left_display_frame, bg="black")
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 结果信息显示
        self.result_info_var = tk.StringVar(value="")
        ttk.Label(left_display_frame, textvariable=self.result_info_var, font=("Arial", 12, "bold")).pack(anchor=tk.CENTER, pady=5)
        
        # 右侧轮胎处理结果显示区域
        right_display_frame = ttk.LabelFrame(display_frame, text="轮胎处理结果")
        right_display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 轮胎处理结果画布
        self.tire_canvas = tk.Canvas(right_display_frame, bg="black")
        self.tire_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 轮胎处理信息显示
        self.tire_info_var = tk.StringVar(value="")
        ttk.Label(right_display_frame, textvariable=self.tire_info_var, font=("Arial", 12, "bold")).pack(anchor=tk.CENTER, pady=5)
    
    def detect_objects(self):
        """使用YOLO模型检测图像中的目标"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择一张图像")
            return
        
        if self.model is None:
            messagebox.showwarning("警告", "请先选择YOLO模型")
            return
        
        try:
            # 更新状态
            self.status_var.set("正在检测目标...")
            
            # 使用YOLO模型进行检测
            results = self.model(self.current_image, conf=0.25)
            self.detection_results = results[0]
            
            # 创建结果图像
            self.result_image = self.current_image.copy()
            
            # 统计检测到的各类别数量
            class_counts = {}
            
            # 清空之前的轮胎边界框
            self.tire_boxes = []
            
            # 处理检测结果
            if self.detection_results.boxes:
                for box in self.detection_results.boxes:
                    # 获取类别
                    cls = int(box.cls[0])
                    cls_name = self.detection_results.names[cls]
                    conf = float(box.conf[0])
                    
                    # 更新类别计数
                    if cls_name in class_counts:
                        class_counts[cls_name] += 1
                    else:
                        class_counts[cls_name] = 1
                    
                    # 获取边界框
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    
                    # 如果是轮胎，存储边界框
                    if cls_name == "tire":
                        self.tire_boxes.append((x1, y1, x2, y2))
                    
                    # 为不同类别设置不同颜色
                    if cls_name == "tire":
                        color = (0, 255, 0)  # 绿色
                    elif cls_name == "person":
                        color = (0, 0, 255)  # 红色
                    elif cls_name == "outer":
                        color = (255, 0, 0)  # 蓝色
                    elif cls_name == "car":
                        color = (255, 255, 0)  # 青色
                    else:
                        color = (255, 0, 255)  # 紫色
                    
                    # 绘制边界框
                    cv2.rectangle(self.result_image, (x1, y1), (x2, y2), color, 2)
                    
                    # 添加标签
                    label = f"{cls_name} {conf:.2f}"
                    cv2.putText(self.result_image, label, (x1, y1 - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 显示结果图像
            result_rgb = cv2.cvtColor(self.result_image, cv2.COLOR_BGR2RGB)
            self.display_image(result_rgb, self.image_canvas)
            
            # 更新状态
            detection_summary = ", ".join([f"{cls}: {count}" for cls, count in class_counts.items()])
            if detection_summary:
                self.status_var.set(f"检测完成: {detection_summary}")
                self.result_info_var.set(f"检测结果: {detection_summary}")
            else:
                self.status_var.set("未检测到任何目标")
                self.result_info_var.set("未检测到任何目标")
            
            # 清空轮胎处理区域
            self.tire_canvas.delete("all")
            self.tire_info_var.set("")
            
            # 如果检测到轮胎，启用轮胎处理按钮，禁用调试按钮
            if "tire" in class_counts and class_counts["tire"] > 0:
                self.process_tire_button.config(state=tk.NORMAL)
                self.debug_next_button.config(state=tk.DISABLED)
            else:
                self.process_tire_button.config(state=tk.DISABLED)
                self.debug_next_button.config(state=tk.DISABLED)
                self.tire_info_var.set("未检测到轮胎")
        
        except Exception as e:
            messagebox.showerror("错误", f"检测目标时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def process_tire_images(self):
        """处理检测到的轮胎图像，找到内部铝合金轮框"""
        if not self.tire_boxes:
            messagebox.showwarning("警告", "未检测到轮胎")
            return
        
        try:
            # 更新状态
            self.status_var.set("正在准备轮胎图像处理...")
            
            # 清空之前的处理结果
            self.processed_tire_images = []
            self.tire_processing_data = []
            
            # 初始化处理步骤
            self.current_tire_index = 0
            self.current_step = 0
            
            # 为每个轮胎准备处理数据
            for i, (x1, y1, x2, y2) in enumerate(self.tire_boxes):
                # 提取轮胎区域
                tire_image = self.current_image[y1:y2, x1:x2].copy()
                
                # 存储轮胎处理数据
                tire_data = {
                    'original': tire_image,
                    'box': (x1, y1, x2, y2),
                    'steps': [],
                    'result': None,
                    'debug_images': []
                }
                
                self.tire_processing_data.append(tire_data)
            
            # 开始第一个轮胎的第一步处理
            self.process_next_step()
            
        except Exception as e:
            messagebox.showerror("错误", f"准备轮胎图像处理时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def process_next_step(self):
        """处理下一个调试步骤"""
        try:
            # 检查是否有轮胎数据
            if not self.tire_processing_data:
                self.debug_next_button.config(state=tk.DISABLED)
                self.reprocess_button.config(state=tk.DISABLED)
                return
            
            # 获取当前轮胎数据
            tire_data = self.tire_processing_data[self.current_tire_index]
            
            # 启用重新处理按钮
            self.reprocess_button.config(state=tk.NORMAL)
            
            if self.current_step == 0:
                # 步骤0: 显示原始轮胎图像
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 显示原始图像")
                original_rgb = cv2.cvtColor(tire_data['original'], cv2.COLOR_BGR2RGB)
                self.display_image(original_rgb, self.tire_canvas)
                self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 原始图像")
                
                # 保存当前步骤结果
                tire_data['step_0'] = tire_data['original'].copy()
                
                self.current_step += 1
        
            elif self.current_step == 1:
                # 步骤1: 转换为灰度图像并显示
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 转换为灰度图像")
                
                # 使用上一步的结果
                if 'gray' not in tire_data:
                    gray_image = cv2.cvtColor(tire_data['original'], cv2.COLOR_BGR2GRAY)
                    tire_data['gray'] = gray_image
                else:
                    gray_image = tire_data['gray']
                
                gray_display = cv2.cvtColor(gray_image, cv2.COLOR_GRAY2BGR)
                cv2.putText(gray_display, "灰度图像", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                gray_rgb = cv2.cvtColor(gray_display, cv2.COLOR_BGR2RGB)
                self.display_image(gray_rgb, self.tire_canvas)
                self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 灰度图像")
                
                # 保存当前步骤结果
                tire_data['step_1'] = gray_image.copy()
                
                self.current_step += 1
                
            elif self.current_step == 2:
                # 步骤2: 应用Canny边缘检测并显示
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 应用Canny边缘检测")
                threshold1 = self.canny_threshold1.get()
                threshold2 = self.canny_threshold2.get()
                
                # 使用上一步的结果
                edges = cv2.Canny(tire_data['gray'], threshold1, threshold2)
                tire_data['edges'] = edges
                
                edges_display = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
                cv2.putText(edges_display, f"边缘检测 (阈值={threshold1},{threshold2})", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                edges_rgb = cv2.cvtColor(edges_display, cv2.COLOR_BGR2RGB)
                self.display_image(edges_rgb, self.tire_canvas)
                self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 边缘检测 (阈值={threshold1},{threshold2})")
                
                # 保存当前步骤结果
                tire_data['step_2'] = edges.copy()
                
                self.current_step += 1
        
            elif self.current_step == 3:
                # 步骤3: 应用形态学闭操作并显示
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 应用形态学闭操作")
                kernel_size = self.morph_kernel_size.get()
                
                # 确保核大小为奇数
                if kernel_size % 2 == 0:
                    kernel_size += 1
                
                # 使用上一步的结果
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                closed = cv2.morphologyEx(tire_data['edges'], cv2.MORPH_CLOSE, kernel)
                tire_data['closed'] = closed
                
                closed_display = cv2.cvtColor(closed, cv2.COLOR_GRAY2BGR)
                cv2.putText(closed_display, f"形态学闭操作 (核大小={kernel_size})", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                closed_rgb = cv2.cvtColor(closed_display, cv2.COLOR_BGR2RGB)
                self.display_image(closed_rgb, self.tire_canvas)
                self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 形态学闭操作 (核大小={kernel_size})")
                
                # 保存当前步骤结果
                tire_data['step_3'] = closed.copy()
                
                self.current_step += 1
                
            elif self.current_step == 4:
                # 步骤4: 轮廓提取并显示
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 轮廓提取")
                
                # 使用上一步的结果
                contours, _ = cv2.findContours(tire_data['closed'], cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 创建轮廓图像
                contour_image = np.zeros_like(tire_data['original'])
                cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)
                tire_data['contours'] = contours
                
                contour_display = contour_image.copy()
                cv2.putText(contour_display, f"轮廓提取 (找到{len(contours)}个轮廓)", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                contour_rgb = cv2.cvtColor(contour_display, cv2.COLOR_BGR2RGB)
                self.display_image(contour_rgb, self.tire_canvas)
                self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 轮廓提取 (找到{len(contours)}个轮廓)")
                
                # 保存当前步骤结果
                tire_data['step_4'] = contour_image.copy()
                
                self.current_step += 1
        
            elif self.current_step == 5:
                # 步骤5: 椭圆拟合并显示最终结果
                self.status_var.set(f"轮胎 {self.current_tire_index+1}/{len(self.tire_processing_data)}: 椭圆拟合")
                
                # 创建结果图像
                result_image = tire_data['original'].copy()
                
                # 检查是否有轮廓
                if not tire_data['contours']:
                    self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 未检测到轮廓，无法拟合椭圆")
                    tire_data['result'] = result_image
                    self.processed_tire_images.append(result_image)
                    
                    # 更新步骤
                    self.current_step = 0  # 重置步骤
                    self.current_tire_index += 1  # 移动到下一个轮胎
                    
                    # 检查是否处理完所有轮胎
                    if self.current_tire_index >= len(self.tire_processing_data):
                        self.show_final_results()
                        return
                    
                    # 继续处理下一个轮胎
                    self.process_next_step()
                    return
                
                # 按面积降序排序，选最大轮廓
                contours = sorted(tire_data['contours'], key=cv2.contourArea, reverse=True)
                largest_contour = contours[0]
                
                # 椭圆拟合
                if len(largest_contour) >= 5:  # fitEllipse需要至少5个点
                    ellipse = cv2.fitEllipse(largest_contour)
                    (cx, cy), (MA, ma), angle = ellipse
                    
                    # 绘制结果
                    cv2.ellipse(result_image, ellipse, (0, 255, 0), 2)
                    cv2.circle(result_image, (int(cx), int(cy)), 5, (0, 0, 255), -1)
                    
                    # 计算椭圆面积
                    area = np.pi * MA * ma / 4
                    
                    # 添加文本信息
                    cv2.putText(result_image, f"中心: ({int(cx)}, {int(cy)})", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    cv2.putText(result_image, f"长轴: {int(MA)}, 短轴: {int(ma)}", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    cv2.putText(result_image, f"角度: {int(angle)}°", (10, 90), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    cv2.putText(result_image, f"面积: {int(area)}", (10, 120), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    
                    # 显示结果
                    result_rgb = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
                    self.display_image(result_rgb, self.tire_canvas)
                    self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 椭圆拟合\n"
                                          f"中心: ({int(cx)}, {int(cy)}), 长轴: {int(MA)}, 短轴: {int(ma)}, 角度: {int(angle)}°")
                    
                    # 在原始图像上标记轮胎中心
                    x1, y1, x2, y2 = tire_data['box']
                    abs_cx = x1 + int(cx)
                    abs_cy = y1 + int(cy)
                    cv2.circle(self.result_image, (abs_cx, abs_cy), 8, (0, 0, 255), -1)
                    cv2.putText(self.result_image, f"轮胎 {self.current_tire_index+1}", (abs_cx + 10, abs_cy), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
                    # 保存椭圆参数到轮胎数据
                    tire_data['ellipse'] = {
                        'center': (cx, cy),
                        'axes': (MA, ma),
                        'angle': angle,
                        'area': area
                    }
                else:
                    cv2.putText(result_image, "轮廓点数不足，无法拟合椭圆", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    self.tire_info_var.set(f"轮胎 {self.current_tire_index+1} - 步骤 {self.current_step+1}: 轮廓点数不足，无法拟合椭圆")
                
                # 保存当前步骤结果
                tire_data['step_5'] = result_image.copy()
                tire_data['result'] = result_image.copy()
                self.processed_tire_images.append(result_image.copy())
                
                # 更新步骤
                self.current_step = 0  # 重置步骤
                self.current_tire_index += 1  # 移动到下一个轮胎

                # 检查是否处理完所有轮胎
                if self.current_tire_index >= len(self.tire_processing_data):
                    self.show_final_results()
                    return

            # 启用下一步按钮
            self.debug_next_button.config(state=tk.NORMAL)
            
        except Exception as e:
            messagebox.showerror("错误", f"处理步骤时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def show_final_results(self):
        """显示所有轮胎处理的最终结果"""
        try:
            # 创建一个拼接图像用于显示所有处理后的轮胎
            max_height = 0
            total_width = 0
            padding = 10  # 图像之间的间距
            
            # 计算拼接图像的尺寸
            for tire_data in self.tire_processing_data:
                result_image = tire_data['result']
                if result_image is not None:
                    h, w = result_image.shape[:2]
                    max_height = max(max_height, h)
                    total_width += w + padding
            
            # 创建拼接图像
            if self.processed_tire_images:
                total_width = max(1, total_width - padding)  # 减去最后一个padding
                concat_image = np.zeros((max_height, total_width, 3), dtype=np.uint8)
                
                # 拼接所有处理后的轮胎图像
                x_offset = 0
                for tire_data in self.tire_processing_data:
                    result_image = tire_data['result']
                    if result_image is not None:
                        h, w = result_image.shape[:2]
                        concat_image[0:h, x_offset:x_offset+w] = result_image
                        x_offset += w + padding
                
                # 显示拼接后的轮胎处理结果
                concat_rgb = cv2.cvtColor(concat_image, cv2.COLOR_BGR2RGB)
                self.display_image(concat_rgb, self.tire_canvas)
                
                # 更新原始图像显示（包含标记的轮胎中心）
                result_rgb = cv2.cvtColor(self.result_image, cv2.COLOR_BGR2RGB)
                self.display_image(result_rgb, self.image_canvas)
                
                # 更新状态
                self.status_var.set(f"轮胎处理完成: 找到 {len(self.processed_tire_images)} 个轮胎")
                self.tire_info_var.set(f"已处理 {len(self.processed_tire_images)} 个轮胎图像")
                
                # 禁用下一步按钮
                self.debug_next_button.config(state=tk.DISABLED)
                self.reprocess_button.config(state=tk.DISABLED)
            else:
                self.status_var.set("未能成功处理轮胎图像")
                self.tire_info_var.set("未能成功处理轮胎图像")
        
        except Exception as e:
            messagebox.showerror("错误", f"显示最终结果时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def display_image(self, image, canvas):
        """在指定画布上显示图像"""
        # 获取画布尺寸
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()
        
        # 如果画布尚未渲染，使用默认尺寸
        if canvas_width <= 1:
            canvas_width = 600
        if canvas_height <= 1:
            canvas_height = 600
        
        # 调整图像大小以适应画布
        h, w = image.shape[:2]
        ratio = min(canvas_width/w, canvas_height/h)
        new_size = (int(w*ratio), int(h*ratio))
        
        # 调整图像大小
        resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_AREA)
        
        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        
        # 转换为PhotoImage
        tk_image = ImageTk.PhotoImage(image=pil_image)
        
        # 保存引用，防止垃圾回收
        canvas.image = tk_image
        
        # 清除画布并显示图像
        canvas.delete("all")
        canvas.create_image(canvas_width//2, canvas_height//2, image=tk_image)
    
    def select_model(self):
        """选择YOLO模型文件"""
        model_file = filedialog.askopenfilename(
            title="选择YOLO模型文件",
            filetypes=[
                ("PyTorch模型", "*.pt"),
                ("ONNX模型", "*.onnx"),
                ("所有文件", "*.*")
            ]
        )
        if model_file:
            self.model_path = model_file
            self.model_path_var.set(model_file)
            self.status_var.set(f"已选择模型: {os.path.basename(model_file)}")
            
            # 尝试加载模型
            try:
                self.model = YOLO(model_file)
                self.status_var.set(f"模型加载成功: {os.path.basename(model_file)}")
            except Exception as e:
                messagebox.showerror("错误", f"加载模型时出错: {str(e)}")
                self.status_var.set(f"错误: {str(e)}")
    
    def select_image(self):
        """选择待处理的图像文件"""
        image_file = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp"),
                ("所有文件", "*.*")
            ]
        )
        if image_file:
            self.image_path_var.set(image_file)
            self.status_var.set(f"已选择图像: {os.path.basename(image_file)}")
            
            # 加载并显示原始图像
            self.load_image(image_file)
    
    def load_image(self, image_path):
        """加载并显示原始图像"""
        try:
            # 使用OpenCV读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("无法读取图像")
            
            # 存储当前图像
            self.current_image = image
            self.result_image = None
            self.tire_boxes = []
            self.processed_tire_images = []
            
            # 转换为RGB用于显示
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 显示在图像画布上
            self.display_image(image_rgb, self.image_canvas)
            
            # 清空轮胎处理区域
            self.tire_canvas.delete("all")
            self.tire_info_var.set("")
            
            # 禁用轮胎处理按钮和调试按钮
            self.process_tire_button.config(state=tk.DISABLED)
            self.debug_next_button.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            
            # 禁用轮胎处理按钮和调试按钮
            self.process_tire_button.config(state=tk.DISABLED)
            self.debug_next_button.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def save_result(self):
        """保存处理结果"""
        if self.result_image is None and not self.processed_tire_images:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        try:
            # 选择保存类型
            save_type = messagebox.askyesno("保存选项", "是否保存轮胎处理结果？\n选择\"是\"保存轮胎处理结果，选择\"否\"保存检测结果")
            
            if save_type and self.processed_tire_images:
                # 保存轮胎处理结果
                for i, tire_img in enumerate(self.processed_tire_images):
                    # 选择保存路径
                    save_path = filedialog.asksaveasfilename(
                        title=f"保存轮胎{i+1}处理结果",
                        defaultextension=".jpg",
                        filetypes=[
                            ("JPEG图像", "*.jpg"),
                            ("PNG图像", "*.png"),
                            ("所有文件", "*.*")
                        ],
                        initialfile=f"tire_{i+1}.jpg"
                    )
                    
                    if save_path:
                        # 保存图像
                        cv2.imwrite(save_path, tire_img)
                        self.status_var.set(f"轮胎{i+1}结果已保存至: {os.path.basename(save_path)}")
                
                messagebox.showinfo("成功", "轮胎处理结果已保存")
            
            elif self.result_image is not None:
                # 保存检测结果
                save_path = filedialog.asksaveasfilename(
                    title="保存检测结果图像",
                    defaultextension=".jpg",
                    filetypes=[
                        ("JPEG图像", "*.jpg"),
                        ("PNG图像", "*.png"),
                        ("所有文件", "*.*")
                    ]
                )
                
                if save_path:
                    # 保存图像
                    cv2.imwrite(save_path, self.result_image)
                    self.status_var.set(f"检测结果已保存至: {os.path.basename(save_path)}")
                    messagebox.showinfo("成功", f"检测结果图像已保存至:\n{save_path}")
            
            else:
                messagebox.showwarning("警告", "没有可保存的结果")
        
        except Exception as e:
            messagebox.showerror("错误", f"保存图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def update_parameters(self):
        """更新处理参数并重新执行当前步骤"""
        try:
            # 如果当前没有处理中的轮胎，则不执行任何操作
            if not self.tire_processing_data or self.current_tire_index >= len(self.tire_processing_data):
                return
            
            # 保存当前步骤和轮胎索引
            current_step = self.current_step
            current_tire_index = self.current_tire_index
            
            # 如果当前步骤已经完成，则回退一步
            if current_step == 0 and current_tire_index > 0:
                self.current_tire_index = current_tire_index - 1
                self.current_step = 4  # 设置为最后一个步骤
            else:
                # 否则回退到当前步骤的开始
                self.current_step = current_step - 1 if current_step > 0 else 0
            
            # 执行当前步骤
            self.process_next_step()
            
            # 启用重新处理按钮
            self.reprocess_button.config(state=tk.NORMAL)
            
        except Exception as e:
            messagebox.showerror("错误", f"更新参数时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def reprocess_current_tire(self):
        """使用当前参数重新处理当前轮胎"""
        try:
            # 如果当前没有处理中的轮胎，则不执行任何操作
            if not self.tire_processing_data or self.current_tire_index >= len(self.tire_processing_data):
                return
            
            # 获取当前轮胎索引
            current_tire_index = self.current_tire_index
            if current_tire_index == 0 and self.current_step == 0:
                # 如果是第一个轮胎的第一步，不需要回退
                pass
            elif self.current_step == 0:
                # 如果是其他轮胎的第一步，回退到上一个轮胎
                current_tire_index -= 1
            
            # 重置当前轮胎的处理数据，但保留原始图像和边界框
            tire_data = self.tire_processing_data[current_tire_index]
            original_image = tire_data['original'].copy()
            box = tire_data['box']
            
            # 重新创建处理数据
            tire_data = {
                'original': original_image,
                'box': box,
                'steps': [],
                'result': None,
                'debug_images': []
            }
            
            # 更新处理数据
            self.tire_processing_data[current_tire_index] = tire_data
            
            # 设置当前索引和步骤
            self.current_tire_index = current_tire_index
            self.current_step = 0
            
            # 重新开始处理
            self.status_var.set(f"正在重新处理轮胎 {current_tire_index+1}...")
            self.process_next_step()
            
        except Exception as e:
            messagebox.showerror("错误", f"重新处理轮胎时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    root = tk.Tk()
    app = TireDetector(root)
    root.mainloop()

# 如果直接运行此脚本
if __name__ == "__main__":
    main()