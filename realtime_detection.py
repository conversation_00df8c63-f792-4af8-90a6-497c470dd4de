from ultralytics import YOLO  # 导入YOLO模型类，用于加载和运行YOLOv11模型
import cv2  # 导入OpenCV库，用于视频捕获和图像处理
import time  # 导入time模块，用于计算FPS
import os  # 导入os模块，用于文件路径操作
import tkinter as tk  # 导入tkinter用于创建GUI界面
from tkinter import ttk, filedialog  # 导入ttk用于现代风格的控件，filedialog用于文件选择
from PIL import Image, ImageTk  # 导入PIL库用于图像处理和在tkinter中显示图像
import threading  # 导入threading用于多线程处理

class YoloDetectionApp:
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("检测")
        self.root.geometry("800x650") # 稍微增加高度以容纳摄像头选项
        
        # 设置应用程序状态变量
        self.is_running = False  # 检测是否正在运行
        self.model = None  # YOLO模型实例
        self.cap = None  # 视频捕获对象
        self.current_frame = None  # 当前处理的帧
        self.seek_position = -1  # 用于视频定位的帧位置，-1表示不需要定位
        
        # 模型路径变量
        self.model_path = tk.StringVar(value=r"F:\python code\yolomodel\yolo11n.pt")
        
        # 视频源变量
        self.video_source = tk.StringVar(value="摄像头")
        self.video_path = tk.StringVar(value="")
        self.camera_index = tk.IntVar(value=0) # 新增：摄像头索引变量
        self.camera_resolution = tk.StringVar(value="720p") # <-- 新增：摄像头分辨率变量
        
        # 检测参数
        self.conf_thres = 0.5  # 置信度阈值
        self.iou_thres = 0.45   # IoU阈值
        
        # 创建GUI界面
        self.create_ui()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 模型选择区域
        model_frame = ttk.LabelFrame(control_frame, text="模型设置", padding=5)
        model_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # 模型路径显示
        ttk.Label(model_frame, text="模型路径:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(model_frame, textvariable=self.model_path, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(model_frame, text="选择模型", command=self.select_model).pack(side=tk.LEFT, padx=5)
        
        # 视频源选择区域
        video_source_frame = ttk.LabelFrame(main_frame, text="视频源", padding=5)
        video_source_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 视频源选择单选按钮
        ttk.Radiobutton(video_source_frame, text="摄像头", variable=self.video_source, value="摄像头", 
                        command=self.toggle_video_source).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(video_source_frame, text="视频文件", variable=self.video_source, value="视频文件", 
                        command=self.toggle_video_source).pack(side=tk.LEFT, padx=5)
        
        # 摄像头选择区域 (初始隐藏)
        self.camera_select_frame = ttk.Frame(video_source_frame)
        ttk.Label(self.camera_select_frame, text="选择摄像头:").pack(side=tk.LEFT, padx=(10, 2))
        ttk.Radiobutton(self.camera_select_frame, text="0", variable=self.camera_index, value=0).pack(side=tk.LEFT)
        ttk.Radiobutton(self.camera_select_frame, text="1", variable=self.camera_index, value=1).pack(side=tk.LEFT)
        ttk.Radiobutton(self.camera_select_frame, text="2", variable=self.camera_index, value=2).pack(side=tk.LEFT)
        
        # --- 新增：分辨率选择 --- 
        ttk.Label(self.camera_select_frame, text="分辨率:").pack(side=tk.LEFT, padx=(15, 2))
        ttk.Radiobutton(self.camera_select_frame, text="720p", variable=self.camera_resolution, value="720p").pack(side=tk.LEFT)
        ttk.Radiobutton(self.camera_select_frame, text="480p", variable=self.camera_resolution, value="480p").pack(side=tk.LEFT)
        # --------------------------

        # 视频文件路径 (内容会被toggle管理显隐)
        self.video_path_frame = ttk.Frame(video_source_frame)
        self.video_path_label = ttk.Label(self.video_path_frame, text="视频文件:")
        self.video_path_label.pack(side=tk.LEFT, padx=5)
        self.video_path_entry = ttk.Entry(self.video_path_frame, textvariable=self.video_path, width=30) # 调整宽度
        self.video_path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.select_video_button = ttk.Button(self.video_path_frame, text="选择视频", command=self.select_video)
        self.select_video_button.pack(side=tk.LEFT, padx=5)
        # self.video_path_frame.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True) # 不在这里 pack
        
        # 视频时间控制区域 - 确保父容器是 main_frame
        self.video_time_frame = ttk.LabelFrame(main_frame, text="视频时间控制", padding=5)
        self.video_time_frame.pack(fill=tk.X, pady=(0, 10))
        self.video_time_frame.pack_forget()  # 初始隐藏
        
        # 视频时间进度条
        self.video_time_var = tk.DoubleVar(value=0.0)
        self.video_time_slider = ttk.Scale(
            self.video_time_frame, 
            from_=0, 
            to=100, 
            orient=tk.HORIZONTAL, 
            variable=self.video_time_var,
            command=self.on_video_time_change
        )
        self.video_time_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 当前时间/总时间显示
        self.video_time_label = ttk.Label(self.video_time_frame, text="00:00:00 / 00:00:00")
        self.video_time_label.pack(side=tk.RIGHT, padx=5)
        
        # 控制按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="控制", padding=5)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 开始和停止按钮
        ttk.Button(button_frame, text="开始检测", command=self.start_detection).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="停止检测", command=self.stop_detection).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # FPS显示
        self.fps_var = tk.StringVar(value="FPS: 0.0")
        ttk.Label(status_frame, textvariable=self.fps_var).pack(side=tk.RIGHT)
        
        # 创建视频显示区域
        video_frame = ttk.LabelFrame(main_frame, text="视频显示")
        video_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建画布用于显示视频
        self.video_canvas = tk.Canvas(video_frame, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 在所有UI元素创建完成后，再根据默认值更新显隐
        self.toggle_video_source() 
    
    def select_model(self):
        """选择YOLO模型文件"""
        model_file = filedialog.askopenfilename(
            title="选择YOLO模型文件",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")],
            initialdir=os.path.dirname(self.model_path.get())
        )
        if model_file:
            self.model_path.set(model_file)
            self.status_var.set(f"已选择模型: {os.path.basename(model_file)}")
    
    def toggle_video_source(self):
        """切换视频源"""
        # 先隐藏所有条件性显示的控件
        self.camera_select_frame.pack_forget()
        self.video_path_frame.pack_forget()
        self.video_time_frame.pack_forget()
        
        video_source_frame = self.camera_select_frame.master # 获取正确的父容器

        if self.video_source.get() == "视频文件":
            # 显示视频文件路径选择 (在其父容器 video_source_frame 内 pack)
            self.video_path_frame.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True, in_=video_source_frame) 
            # 显示时间控制条 (在其父容器 main_frame 内 pack，位于 video_source_frame 之后)
            self.video_time_frame.pack(fill=tk.X, pady=(0, 10), after=video_source_frame)
        else: # 选择摄像头
            # 显示摄像头选择 (在其父容器 video_source_frame 内 pack)
            self.camera_select_frame.pack(side=tk.LEFT, padx=5, in_=video_source_frame)
            # video_time_frame 保持隐藏
    
    def select_video(self):
        """选择视频文件"""
        video_file = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        if video_file:
            self.video_path.set(video_file)
            self.status_var.set(f"已选择视频: {os.path.basename(video_file)}")
            
            # 获取视频时长并更新进度条
            self.update_video_duration(video_file)
    
    def update_video_duration(self, video_path):
        """获取视频时长并更新进度条"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise IOError("无法打开视频文件")
                
            # 获取视频总帧数和帧率
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 计算视频总时长（秒）
            if fps > 0:
                total_duration = total_frames / fps
                
                # 更新进度条最大值
                self.video_time_slider.configure(to=total_duration)
                
                # 更新时间标签
                self.update_time_label(0, total_duration)
                
                # 存储视频信息
                self.video_info = {
                    'total_frames': total_frames,
                    'fps': fps,
                    'duration': total_duration
                }
                
                self.status_var.set(f"视频已加载: {os.path.basename(video_path)} ({self.format_time(total_duration)})")
            else:
                self.status_var.set("警告: 无法获取视频帧率")
                
            cap.release()
            
        except Exception as e:
            self.status_var.set(f"获取视频信息出错: {str(e)}")
    
    def on_video_time_change(self, value):
        """视频时间滑块变化时的回调"""
        if hasattr(self, 'video_info'):
            current_time = float(value)
            total_time = self.video_info['duration']
            
            # 更新时间标签
            self.update_time_label(current_time, total_time)
            
            # 如果正在检测中，则更新视频位置
            if self.is_running and self.video_source.get() == "视频文件" and self.cap is not None:
                # 设置标志，通知检测线程需要重新定位视频
                self.seek_position = int(current_time * self.video_info['fps'])
                # 不直接在这里设置位置，而是让检测线程处理
                # 删除错误的行: self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
    
    def update_time_label(self, current_time, total_time):
        """更新时间标签显示"""
        current_str = self.format_time(current_time)
        total_str = self.format_time(total_time)
        self.video_time_label.config(text=f"{current_str} / {total_str}")
    
    def format_time(self, seconds):
        """将秒数格式化为时:分:秒格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def start_detection(self):
        """开始目标检测"""
        if self.is_running:
            return
        
        # 检查模型路径是否存在
        if not os.path.exists(self.model_path.get()):
            self.status_var.set("错误: 模型文件不存在")
            return
        
        # 启动检测线程
        self.is_running = True
        self.detection_thread = threading.Thread(target=self.detection_loop)
        self.detection_thread.daemon = True
        self.detection_thread.start()
        
        # 启动UI更新线程
        self.update_ui()
    
    def stop_detection(self):
        """停止目标检测"""
        self.is_running = False
        if hasattr(self, 'detection_thread') and self.detection_thread.is_alive():
            self.detection_thread.join(timeout=1.0)
        
        # 释放摄像头资源
        if self.cap and self.cap.isOpened():
            self.cap.release()
            self.cap = None
        
        self.status_var.set("检测已停止")
    
    def detection_loop(self):
        """检测循环 - 在单独的线程中运行"""
        try:
            self.status_var.set("正在加载模型...")
            
            # 加载YOLO模型
            self.model = YOLO(self.model_path.get())
            
            # 初始化视频源
            source_info = "" # 用于存储源名称和分辨率
            if self.video_source.get() == "摄像头":
                cam_idx = self.camera_index.get()
                # 使用 DirectShow 后端
                self.cap = cv2.VideoCapture(cam_idx, cv2.CAP_DSHOW)
                
                # --- 新增：设置请求的分辨率 --- 
                selected_res = self.camera_resolution.get()
                if selected_res == "720p":
                    desired_width = 1280
                    desired_height = 720
                elif selected_res == "480p":
                    desired_width = 640
                    desired_height = 480
                else: # 默认或意外情况
                    desired_width = 1280
                    desired_height = 720
                    print(f"[Warning] 无效的摄像头分辨率选项 '{selected_res}', 使用默认 720p.")
                
                print(f"尝试设置摄像头分辨率为: {desired_width}x{desired_height}")
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, desired_width)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, desired_height)
                # -------------------------------
                
                if not self.cap.isOpened(): # 检查摄像头是否成功打开
                     raise IOError(f"无法打开摄像头 {cam_idx}")
                source_info = f"摄像头 {cam_idx}"
            else:
                video_path = self.video_path.get()
                if not video_path or not os.path.exists(video_path):
                    raise ValueError("请选择有效的视频文件")
                self.cap = cv2.VideoCapture(video_path)
                
                # 设置视频起始位置
                if hasattr(self, 'video_info'):
                    start_time = self.video_time_var.get()
                    start_frame = int(start_time * self.video_info['fps'])
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
                    
                source_info = f"视频文件: {os.path.basename(video_path)}"
            
            if not self.cap.isOpened():
                raise IOError("无法打开视频源")

            # --- 新增：获取并显示分辨率 ---
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            resolution_str = ""
            if width > 0 and height > 0:
                resolution_str = f" ({width}x{height})"
            
            # 更新状态栏，包含分辨率
            self.status_var.set(f"从 {source_info} 检测中...{resolution_str}")
            # --------------------------

            # 获取视频总帧数（仅对视频文件有效）
            total_frames = 0
            if self.video_source.get() == "视频文件":
                total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # FPS计算变量
            fps = 0
            frame_count = 0
            start_time = time.time()
            
            # 检测循环
            while self.is_running:
                # 检查是否需要重新定位视频
                if self.seek_position >= 0 and self.video_source.get() == "视频文件":
                    # 安全地重新打开视频文件以避免解码器问题
                    video_path = self.video_path.get()
                    old_cap = self.cap
                    self.cap = cv2.VideoCapture(video_path)
                    if not self.cap.isOpened():
                        self.cap = old_cap  # 如果新打开失败，恢复旧的
                        self.seek_position = -1
                        continue
                        
                    # 设置新的位置
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.seek_position)
                    self.seek_position = -1  # 重置标志
                    
                    # 释放旧的视频捕获对象
                    if old_cap and old_cap != self.cap:
                        old_cap.release()
                    
                    # 重置FPS计算
                    frame_count = 0
                    start_time = time.time()
                
                # 读取一帧
                ret, frame = self.cap.read()
                if not ret:
                    # 如果是视频文件播放完毕，可以选择循环播放
                    if self.video_source.get() == "视频文件":
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到视频开始
                        continue
                    else:
                        break
                
                # 计算FPS
                frame_count += 1
                elapsed_time = time.time() - start_time
                if elapsed_time >= 1.0:
                    fps = frame_count / elapsed_time
                    self.fps_var.set(f"FPS: {fps:.1f}")
                    
                    # 对于视频文件，更新进度条和时间显示
                    if self.video_source.get() == "视频文件" and hasattr(self, 'video_info'):
                        current_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                        current_time = current_frame / self.video_info['fps']
                        
                        # 更新进度条（不触发回调）
                        self.root.after(0, lambda: self.video_time_var.set(current_time))
                        
                        # 更新时间标签
                        self.root.after(0, lambda: self.update_time_label(current_time, self.video_info['duration']))
                        
                        # 更新状态栏
                        progress = (current_frame / total_frames) * 100
                        self.status_var.set(f"检测中: {progress:.1f}% ({current_frame}/{total_frames})")
                    
                    frame_count = 0
                    start_time = time.time()
                
                # 使用YOLO模型进行目标检测
                results = self.model.predict(
                    source=frame,
                    conf=self.conf_thres,
                    iou=self.iou_thres,
                    verbose=False,
                    device="cuda:0",  # 使用GPU进行推理
                    # device="cpu",  # 使用CPU进行推理
                    stream=True  # 启用流式推理
                )
                
                # 处理检测结果
                for result in results:
                    annotated_frame = result.plot()
                    break
                
                # 显示FPS
                cv2.putText(
                    annotated_frame,
                    f"FPS: {fps:.1f}",
                    (20, 30),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    1,
                    (0, 255, 0),
                    2
                )
                
                # 更新当前帧
                self.current_frame = annotated_frame.copy()
                
                # 短暂休眠，减少CPU使用
                time.sleep(0.01)
            
        except Exception as e:
            self.status_var.set(f"错误: {str(e)}")
            print(f"检测错误: {str(e)}")
        finally:
            # 确保资源被释放
            if self.cap and self.cap.isOpened():
                self.cap.release()
            
            self.is_running = False
    
    def update_ui(self):
        """更新UI界面 - 在主线程中运行"""
        if not self.is_running:
            return
        
        # 如果有新帧，更新显示
        if self.current_frame is not None:
            # 转换OpenCV图像到Tkinter可显示的格式
            frame_rgb = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(frame_rgb)
            
            # 调整图像大小以适应画布
            canvas_width = self.video_canvas.winfo_width()
            canvas_height = self.video_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:  # 确保画布已经渲染
                # 保持宽高比
                img_width, img_height = img.size
                ratio = min(canvas_width/img_width, canvas_height/img_height)
                new_size = (int(img_width*ratio), int(img_height*ratio))
                img = img.resize(new_size, Image.LANCZOS)
                
                # 转换为PhotoImage
                self.photo = ImageTk.PhotoImage(image=img)
                
                # 在画布上显示图像
                self.video_canvas.delete("all")
                self.video_canvas.create_image(
                    canvas_width//2, canvas_height//2,
                    image=self.photo, anchor=tk.CENTER
                )
        
        # 每30毫秒更新一次UI (约33 FPS)
        self.root.after(30, self.update_ui)
    
    def on_closing(self):
        """窗口关闭时的处理"""
        self.stop_detection()
        self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = YoloDetectionApp(root)
    root.mainloop()

# 程序入口点
if __name__ == "__main__":
    main()
