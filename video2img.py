import cv2
import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, StringVar
from pathlib import Path
import threading
import time
from PIL import Image, ImageTk

class VideoToImageApp:
    def __init__(self, root):
        self.root = root
        self.root.title("视频转图片")
        # 调整窗口初始大小，增加高度
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 视频文件路径
        self.video_path = None
        
        # 视频参数
        self.video_width = 0
        self.video_height = 0
        self.video_fps = 0
        self.video_frames = 0
        self.video_duration = 0  # 单位：秒
        
        # 自定义提取帧率
        self.extract_fps = StringVar(value="1")
        
        # 添加时间区间变量
        self.start_time = StringVar(value="0:0")
        self.end_time = StringVar(value="0:0")
        
        # 转换状态
        self.is_converting = False
        
        # 视频播放相关变量
        self.cap = None
        self.current_frame = None
        self.is_playing = False
        self.play_thread = None
        self.current_frame_position = 0
        self.current_time = StringVar(value="0:00")
        
        # 创建UI
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 视频选择区域
        video_select_frame = ttk.LabelFrame(main_frame, text="视频选择", padding=10)
        video_select_frame.pack(fill=tk.X, pady=5)
        
        # 视频路径和选择按钮
        self.video_path_var = StringVar(value="未选择视频文件")
        ttk.Label(video_select_frame, textvariable=self.video_path_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(video_select_frame, text="选择视频", command=self.select_video).pack(side=tk.RIGHT, padx=5)
        
        # 添加视频预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="视频预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 视频画布
        self.video_canvas = tk.Canvas(preview_frame, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 视频控制区域
        video_control_frame = ttk.Frame(preview_frame)
        video_control_frame.pack(fill=tk.X, pady=5)
        
        # 播放/暂停按钮（合并为一个按钮）
        self.play_pause_button = ttk.Button(video_control_frame, text="播放", command=self.toggle_play_pause, state=tk.DISABLED)
        self.play_pause_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(video_control_frame, text="停止", command=self.stop_video, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 当前时间显示
        ttk.Label(video_control_frame, text="当前时间:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Label(video_control_frame, textvariable=self.current_time).pack(side=tk.LEFT, padx=5)
        
        # 设置开始/结束时间按钮
        ttk.Button(video_control_frame, text="设为开始时间", command=self.set_start_time).pack(side=tk.LEFT, padx=(20, 5))
        ttk.Button(video_control_frame, text="设为结束时间", command=self.set_end_time).pack(side=tk.LEFT, padx=5)
        
        # 视频进度条
        self.video_progress_var = tk.DoubleVar(value=0)
        self.video_progress = ttk.Scale(preview_frame, from_=0, to=100, orient=tk.HORIZONTAL, 
                                       variable=self.video_progress_var, command=self.on_progress_change)
        self.video_progress.pack(fill=tk.X, pady=5)
        self.video_progress.config(state=tk.DISABLED)
        
        # 转换设置区域
        convert_frame = ttk.LabelFrame(main_frame, text="转换设置", padding=10)
        convert_frame.pack(fill=tk.X, pady=5)
        
        # 提取帧率设置
        ttk.Label(convert_frame, text="提取帧率:").pack(side=tk.LEFT, padx=5)
        fps_entry = ttk.Entry(convert_frame, textvariable=self.extract_fps, width=5)
        fps_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(convert_frame, text="帧/秒").pack(side=tk.LEFT)
        
        # 添加视频区间提取设置
        ttk.Label(convert_frame, text="  区间:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(convert_frame, textvariable=self.start_time, width=6).pack(side=tk.LEFT, padx=2)
        ttk.Label(convert_frame, text="-").pack(side=tk.LEFT)
        ttk.Entry(convert_frame, textvariable=self.end_time, width=6).pack(side=tk.LEFT, padx=2)
        ttk.Label(convert_frame, text="(分:秒)").pack(side=tk.LEFT)  # 更新提示文本
        
        # 转换按钮
        self.convert_button = ttk.Button(convert_frame, text="转换为图片", command=self.start_conversion, state=tk.DISABLED)
        self.convert_button.pack(side=tk.RIGHT, padx=5)
        
        # 状态和进度区域
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding=10)
        status_frame.pack(fill=tk.X, pady=5)
        
        # 状态信息
        self.status_var = StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 输出区域 - 减小高度以适应视频预览
        output_frame = ttk.LabelFrame(main_frame, text="输出信息", padding=10)
        output_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 输出文本框，用于显示详细信息，减小高度
        self.output_text = tk.Text(output_frame, height=8, width=70)
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.output_text, command=self.output_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.output_text.config(yscrollcommand=scrollbar.set)
        
        # 确保在关闭窗口时释放资源
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def select_video(self):
        """选择视频文件"""
        video_file = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        
        if not video_file:
            return
        
        # 如果之前有视频在播放，先停止
        self.stop_video()
        
        self.video_path = video_file
        self.video_path_var.set(os.path.basename(video_file))
        
        # 读取视频信息
        self.read_video_info()
        
        # 加载视频第一帧
        self.load_video_first_frame()
    
    def read_video_info(self):
        """读取视频文件信息"""
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(self.video_path)
            
            if not cap.isOpened():
                self.log_message("错误: 无法打开视频文件")
                return
            
            # 获取视频属性
            self.video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.video_fps = cap.get(cv2.CAP_PROP_FPS)
            self.video_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算时长
            if self.video_fps > 0:
                self.video_duration = self.video_frames / self.video_fps
                # 更新结束时间为视频总时长（分:秒格式）
                total_minutes = int(self.video_duration // 60)
                total_seconds = int(self.video_duration % 60)
                self.end_time.set(f"{total_minutes}:{total_seconds}")
            
            # 启用转换按钮
            self.convert_button.config(state=tk.NORMAL)
            
            # 启用视频控制按钮
            self.play_pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)
            self.video_progress.config(state=tk.NORMAL)
            
            # 记录日志
            self.log_message(f"已读取视频信息: {os.path.basename(self.video_path)}")
            self.log_message(f"分辨率: {self.video_width}x{self.video_height}, 帧率: {self.video_fps:.2f} fps")
            self.log_message(f"总帧数: {self.video_frames}, 时长: {self.video_duration:.2f} 秒")
            
            # 释放视频
            cap.release()
            
        except Exception as e:
            self.log_message(f"读取视频信息出错: {str(e)}")
    
    def load_video_first_frame(self):
        """加载视频第一帧并显示"""
        try:
            cap = cv2.VideoCapture(self.video_path)
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                self.display_frame(frame)
                self.current_frame = frame
                self.current_frame_position = 0
                self.update_time_display(0)
            else:
                self.log_message("无法读取视频第一帧")
        except Exception as e:
            self.log_message(f"加载视频第一帧出错: {str(e)}")
    
    def display_frame(self, frame):
        """在画布上显示视频帧"""
        if frame is None:
            return
        
        # 获取画布尺寸
        canvas_width = self.video_canvas.winfo_width()
        canvas_height = self.video_canvas.winfo_height()
        
        # 如果画布尚未渲染，使用默认尺寸
        if canvas_width <= 1:
            canvas_width = 640
        if canvas_height <= 1:
            canvas_height = 360
        
        # 调整图像大小以适应画布
        h, w = frame.shape[:2]
        ratio = min(canvas_width/w, canvas_height/h)
        new_size = (int(w*ratio), int(h*ratio))
        
        # 调整图像大小
        resized_frame = cv2.resize(frame, new_size, interpolation=cv2.INTER_AREA)
        
        # 转换为RGB
        rgb_frame = cv2.cvtColor(resized_frame, cv2.COLOR_BGR2RGB)
        
        # 转换为PIL图像
        pil_image = Image.fromarray(rgb_frame)
        
        # 转换为PhotoImage
        tk_image = ImageTk.PhotoImage(image=pil_image)
        
        # 保存引用，防止垃圾回收
        self.video_canvas.image = tk_image
        
        # 清除画布并显示图像
        self.video_canvas.delete("all")
        self.video_canvas.create_image(canvas_width//2, canvas_height//2, image=tk_image)
    
    def toggle_play_pause(self):
        """切换播放/暂停状态"""
        if self.video_path is None:
            return
        
        if self.is_playing:
            # 当前正在播放，切换到暂停
            self.pause_video()
        else:
            # 当前已暂停，切换到播放
            self.play_video()
    
    def play_video(self):
        """播放视频"""
        if self.video_path is None:
            return
        
        # 如果已经在播放，则不做任何操作
        if self.is_playing:
            return
        
        # 如果视频捕获未初始化，则初始化
        if self.cap is None:
            self.cap = cv2.VideoCapture(self.video_path)
            
            # 设置到当前帧位置
            if self.current_frame_position > 0:
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame_position)
        
        # 设置播放状态
        self.is_playing = True
        
        # 更新按钮状态和文本
        self.play_pause_button.config(text="暂停")
        
        # 启动播放线程
        if self.play_thread is None or not self.play_thread.is_alive():
            self.play_thread = threading.Thread(target=self.video_playback_thread)
            self.play_thread.daemon = True
            self.play_thread.start()
    
    def video_playback_thread(self):
        """视频播放线程"""
        try:
            while self.is_playing and self.cap is not None:
                # 读取下一帧
                ret, frame = self.cap.read()
                
                if not ret:
                    # 到达视频末尾，停止播放
                    self.root.after(0, self.stop_video)
                    break
                
                # 更新当前帧
                self.current_frame = frame
                self.current_frame_position = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                
                # 计算当前时间
                current_time = self.current_frame_position / self.video_fps
                
                # 更新进度条
                progress = (current_time / self.video_duration) * 100
                self.root.after(0, lambda p=progress: self.video_progress_var.set(p))
                
                # 更新时间显示
                self.root.after(0, lambda t=current_time: self.update_time_display(t))
                
                # 显示帧
                self.root.after(0, lambda f=frame: self.display_frame(f))
                
                # 控制播放速度
                time.sleep(1 / self.video_fps)
        
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"播放视频时出错: {str(e)}"))
    
    def pause_video(self):
        """暂停视频播放"""
        if not self.is_playing:
            return
        
        # 设置暂停状态
        self.is_playing = False
        
        # 更新按钮状态和文本
        self.play_pause_button.config(text="播放")
        
        # 删除这些行，因为它们引用了不存在的按钮
        # self.play_button.config(state=tk.NORMAL)
        # self.pause_button.config(state=tk.DISABLED)
    
    def stop_video(self):
        """停止视频播放"""
        # 设置停止状态
        self.is_playing = False
        
        # 关闭视频捕获
        if self.cap is not None:
            self.cap.release()
            self.cap = None
        
        # 重置到第一帧
        if self.video_path:
            self.load_video_first_frame()
        
        # 更新按钮状态
        self.play_pause_button.config(text="播放")
        self.video_progress_var.set(0)
    
    def on_progress_change(self, value):
        """处理进度条变化"""
        if self.video_path is None:
            return
        
        # 暂停视频播放
        was_playing = self.is_playing
        if was_playing:
            self.pause_video()
        
        # 计算新的帧位置
        new_time = float(value) / 100 * self.video_duration
        new_frame_position = int(new_time * self.video_fps)
        
        # 打开视频捕获（如果未打开）
        if self.cap is None:
            self.cap = cv2.VideoCapture(self.video_path)
        
        # 设置视频位置
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, new_frame_position)
        
        # 读取新帧
        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame
            self.current_frame_position = new_frame_position
            self.display_frame(frame)
            self.update_time_display(new_time)
        
        # 如果之前在播放，则继续播放
        if was_playing:
            self.play_video()
    
    def update_time_display(self, seconds):
        """更新时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        self.current_time.set(f"{minutes}:{seconds:02d}")
    
    def set_start_time(self):
        """将当前时间设置为开始时间"""
        if self.video_path is None:
            return
        
        # 计算当前时间
        current_time = self.current_frame_position / self.video_fps
        minutes = int(current_time // 60)
        seconds = int(current_time % 60)
        
        # 设置开始时间
        self.start_time.set(f"{minutes}:{seconds}")
        self.log_message(f"已设置开始时间: {minutes}:{seconds}")
    
    def set_end_time(self):
        """将当前时间设置为结束时间"""
        if self.video_path is None:
            return
        
        # 计算当前时间
        current_time = self.current_frame_position / self.video_fps
        minutes = int(current_time // 60)
        seconds = int(current_time % 60)
        
        # 设置结束时间
        self.end_time.set(f"{minutes}:{seconds}")
        self.log_message(f"已设置结束时间: {minutes}:{seconds}")
    
    def start_conversion(self):
        """开始转换过程"""
        if self.is_converting:
            return
        
        if not self.video_path:
            messagebox.showwarning("警告", "请先选择视频文件")
            return
        
        try:
            # 获取用户设置的帧率
            extract_fps = float(self.extract_fps.get())
            
            if extract_fps <= 0:
                messagebox.showwarning("警告", "提取帧率必须大于0")
                return
            
            # 解析时间区间（分:秒格式）
            try:
                # 尝试解析分:秒格式
                start_parts = self.start_time.get().split(':')
                end_parts = self.end_time.get().split(':')
                
                if len(start_parts) == 2:
                    start_minutes = int(start_parts[0])
                    start_seconds = float(start_parts[1])
                    start_time = start_minutes * 60 + start_seconds
                else:
                    start_time = float(self.start_time.get())
                
                if len(end_parts) == 2:
                    end_minutes = int(end_parts[0])
                    end_seconds = float(end_parts[1])
                    end_time = end_minutes * 60 + end_seconds
                else:
                    end_time = float(self.end_time.get())
            except:
                # 如果解析失败，尝试直接转换为浮点数（向后兼容）
                start_time = float(self.start_time.get())
                end_time = float(self.end_time.get())
            
            # 验证时间区间
            if start_time < 0:
                start_time = 0
                self.start_time.set("0")
            
            if end_time <= 0 or end_time > self.video_duration:
                end_time = self.video_duration
                self.end_time.set(str(round(self.video_duration, 2)))
            
            if start_time >= end_time:
                messagebox.showwarning("警告", "开始时间必须小于结束时间")
                return
                
            # 设置转换状态
            self.is_converting = True
            self.convert_button.config(state=tk.DISABLED)
            self.status_var.set("转换中...")
            self.progress_var.set(0)
            
            # 创建转换线程
            thread = threading.Thread(target=self.convert_video_to_images, args=(extract_fps, start_time, end_time))
            thread.daemon = True
            thread.start()
            
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的帧率和时间区间")
    
    def on_close(self):
        """关闭窗口时的处理"""
        # 停止视频播放
        self.is_playing = False
        
        # 释放视频资源
        if self.cap is not None:
            self.cap.release()
        
        # 关闭窗口
        self.root.destroy()
    
    # 以下方法保持不变
    def convert_video_to_images(self, extract_fps, start_time=0, end_time=0):
        """将视频转换为图像序列"""
        try:
            # 创建保存目录
            video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            output_dir = os.path.join(os.path.dirname(self.video_path), video_name)
            
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            self.log_message(f"创建输出目录: {output_dir}")
            
            # 打开视频
            cap = cv2.VideoCapture(self.video_path)
            
            if not cap.isOpened():
                self.log_message("错误: 无法打开视频文件进行转换")
                self.finish_conversion()
                return
            
            # 计算帧提取间隔
            if extract_fps >= self.video_fps:
                # 如果提取帧率大于或等于原始帧率，则提取每一帧
                frame_interval = 1
                self.log_message(f"提取帧率 ({extract_fps}) 大于或等于原始帧率 ({self.video_fps})，将提取每一帧")
            else:
                # 否则，计算间隔
                frame_interval = int(self.video_fps / extract_fps)
                self.log_message(f"将按每 {frame_interval} 帧提取一次，约 {extract_fps:.2f} fps")
            
            # 计算开始和结束帧
            start_frame = int(start_time * self.video_fps)
            end_frame = int(end_time * self.video_fps)
            
            if end_frame <= 0 or end_frame > self.video_frames:
                end_frame = self.video_frames
            
            self.log_message(f"提取时间区间: {start_time:.2f}秒 - {end_time:.2f}秒")
            self.log_message(f"对应帧范围: {start_frame} - {end_frame}")
            
            # 设置视频位置到开始帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # 开始提取
            frame_count = start_frame
            saved_count = 0
            
            while frame_count < end_frame:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # 根据间隔提取帧
                if (frame_count - start_frame) % frame_interval == 0:
                    # 计算当前帧对应的时间(秒)
                    current_time = frame_count / self.video_fps
                    
                    # 计算起始和结束时间的分钟和秒
                    start_minutes = int(start_time // 60)
                    start_seconds = int(start_time % 60)
                    end_minutes = int(end_time // 60)
                    end_seconds = int(end_time % 60)
                    
                    # 修改保存图像的命名方式，使用视频名_起始时间m起始秒s结束时间m结束秒s_序号.jpg
                    image_path = os.path.join(output_dir, 
                        f"{video_name}_{start_minutes}m{start_seconds}s{end_minutes}m{end_seconds}s_{saved_count:06d}.jpg")
                    cv2.imwrite(image_path, frame)
                    saved_count += 1
                    
                    # 更新进度
                    progress = ((frame_count - start_frame) / (end_frame - start_frame)) * 100
                    self.update_progress(progress, f"已提取 {saved_count} 帧")
                
                frame_count += 1
            
            # 完成
            self.log_message(f"转换完成! 共提取 {saved_count} 帧图像")
            self.log_message(f"时间区间: {start_time:.2f}秒 - {end_time:.2f}秒")
            self.log_message(f"输出目录: {output_dir}")
            
            # 添加明显的分隔线和可复制的路径信息
            self.log_message("="*50)
            self.log_message("图片保存位置:")
            self.log_message(f"{output_dir}")
            self.log_message("="*50)
            
            # 释放视频
            cap.release()
            
            # 更新UI
            self.finish_conversion()
            
            # 完成消息
            messagebox.showinfo("完成", f"视频已成功转换为图像!\n共提取 {saved_count} 帧\n时间区间: {start_time:.2f}秒 - {end_time:.2f}秒\n保存至: {output_dir}")
            
        except Exception as e:
            self.log_message(f"转换过程出错: {str(e)}")
            self.finish_conversion()
    
    def update_progress(self, progress_value, status_text):
        """更新进度和状态信息"""
        self.root.after(0, lambda: self.progress_var.set(progress_value))
        self.root.after(0, lambda: self.status_var.set(status_text))
    
    def finish_conversion(self):
        """完成转换，重置状态"""
        self.root.after(0, lambda: self.convert_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.status_var.set("就绪"))
        self.root.after(0, lambda: self.progress_var.set(100))
        self.is_converting = False
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.root.after(0, lambda: self.output_text.insert(tk.END, message + "\n"))
        self.root.after(0, lambda: self.output_text.see(tk.END))

def main():
    root = tk.Tk()
    app = VideoToImageApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()