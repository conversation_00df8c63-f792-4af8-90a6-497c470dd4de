# 地面距离测量系统 - 关键步骤说明

## 1. 系统初始化
- 初始化相机参数（相机矩阵和畸变系数）
- 设置地面矩形实际尺寸（7m×4m）
- 定义地面控制点坐标系统

## 2. 图像处理流程
- 加载原始图像
- 使用相机参数进行畸变矫正
- 调整图像大小以适应界面显示

## 3. 控制点设置
- 用户在界面上标记四个控制点（矩形的四个角点）
- 标记控制点的顺序必须为：
  1. 左上角点
  2. 右上角点
  3. 右下角点
  4. 左下角点
- 这个顺序对应地面坐标系统中的：
  1. (0, 0)
  2. (7m, 0)
  3. (7m, 4m)
  4. (0, 4m)
- 将界面坐标转换为原始图像坐标
- 计算透视变换矩阵（从图像坐标到地面实际坐标）

## 4. 距离测量方法
- 单点测量：计算点到左边线和底边的距离
  - 将图像点坐标通过透视变换转换为地面实际坐标
  - 计算转换后坐标到左边和底边的距离
  
- 两点测距：计算两点之间的实际距离
  - 将两个图像点坐标转换为地面实际坐标
  - 计算两点之间的欧氏距离

## 5. 可视化结果
- 在原始图像上绘制控制点、测量点和距离线
- 显示计算得到的距离值
- 在界面上更新结果显示

## 6. 关键算法
- 图像畸变矫正：cv2.undistort()
- 透视变换矩阵计算：cv2.getPerspectiveTransform()
- 点坐标透视变换：cv2.perspectiveTransform()
- 欧氏距离计算：np.sqrt((x2-x1)**2 + (y2-y1)**2)

## 7. 注意事项
- 控制点标记顺序：左上、右上、右下、左下
- 相机参数对畸变矫正的影响
- 透视变换矩阵对距离计算的准确性影响


透视变换矩阵 H:
  0.004225   0.002904  -1.880735
 -0.005388   0.014707  -1.007583
 -0.000989   0.001145   1.000000

透视变换矩阵 H:
  0.004237   0.002886  -1.872966
 -0.005486   0.014857  -1.014861
 -0.001006   0.001177   1.000000


          
 计算单点到边线距离           
       # 将点坐标转换为适合透视变换的格式
        point = np.array([[[x_pixel, y_pixel]]], dtype=np.float32)
        # 应用透视变换，将图像坐标转换为地面实际坐标
        ground_point = cv2.perspectiveTransform(point, self.H)
        x, y = ground_point[0][0]
        
        # 计算点到左边和底边的实际距离
        return {
            "left": x,  # 水平方向距离左边的距离
            "bottom": self.ground_height - y  # 垂直方向距离底边的距离
        }

现在问题，透视变换矩阵
