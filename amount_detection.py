from ultralytics import YOLO
import cv2
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk, ImageDraw, ImageFont
import numpy as np
import time
import torch

class AmountDetectionApp:
    def __init__(self, root):
        """初始化金额识别应用程序"""
        self.root = root
        self.root.title("加油机屏幕识别")
        self.root.geometry("800x600")
        
        # 模型路径
        self.model_path = r"F:\python code\yolomodel\digit_best.pt"
        
        # 检测参数
        self.conf_thres = 0.25  # 置信度阈值
        self.iou_thres = 0.45   # IoU阈值
        
        # 设备选择：检测是否有可用的GPU，如果有则使用GPU
        self.device = self.get_device()
        
        # 当前图像
        self.current_image = None
        self.display_image_with_results = None
        
        # ROI坐标
        self.roi_amount_coords = (0, 0, 0, 0)
        self.roi_volume_coords = (0, 0, 0, 0)
        
        # ROI选择状态
        self.selecting_roi = False
        self.roi_start_x = 0
        self.roi_start_y = 0
        self.roi_end_x = 0
        self.roi_end_y = 0
        self.roi_rectangle_id = None
        
        # 当前ROI选择模式
        self.roi_mode = "amount"  # "amount" 或 "volume"
        
        # 图像缩放比例
        self.scale_factor = 1.0
        
        # 初始化结果变量
        self.amount_result = ""
        self.volume_result = ""
        self.price_result = ""
        self.all_detected_digits = []
        self.roi_saved = False
        
        # 视频处理相关变量
        self.is_processing_video = False
        self.is_video_paused = False
        self.video_capture = None
        self.video_delay = 100  # 视频帧处理延迟（毫秒）

        # 创建GUI界面
        self.create_ui()
        
        # 加载模型
        self.load_model()
        
        # 中文字体路径 - 使用系统默认中文字体
        self.font_path = os.path.join(os.environ['SYSTEMROOT'], 'Fonts', 'simhei.ttf')
        if not os.path.exists(self.font_path):
            # 如果找不到黑体，尝试使用其他常见中文字体
            alternate_fonts = [
                os.path.join(os.environ['SYSTEMROOT'], 'Fonts', 'msyh.ttc'),  # 微软雅黑
                os.path.join(os.environ['SYSTEMROOT'], 'Fonts', 'simsun.ttc'),  # 宋体
                os.path.join(os.environ['SYSTEMROOT'], 'Fonts', 'simkai.ttf')   # 楷体
            ]
            for font in alternate_fonts:
                if os.path.exists(font):
                    self.font_path = font
                    break
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(
            control_frame, 
            text="选择图片文件", 
            command=self.select_image,
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        # 视频文件选择按钮
        self.video_button = ttk.Button(
            control_frame, 
            text="打开视频文件", 
            command=self.select_video,
            width=20,
            state=tk.DISABLED
        )
        self.video_button.pack(side=tk.LEFT, padx=5)
        
        # 重置ROI按钮
        self.roi_button = ttk.Button(
            control_frame, 
            text="重新选择识别区域", 
            command=self.start_roi_selection,
            width=20,
            state=tk.DISABLED
        )
        self.roi_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="请选择一张加油机屏幕图片")
        ttk.Label(
            control_frame, 
            textvariable=self.status_var,
            font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=10)
        
        # 创建图像显示区域 - 只保留一个画布
        image_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图像画布
        self.canvas = tk.Canvas(image_frame, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件用于ROI选择
        self.canvas.bind("<ButtonPress-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_move)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        
        # 创建视频控制区域
        video_control_frame = ttk.Frame(main_frame)
        video_control_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 播放/暂停按钮
        self.play_pause_button = ttk.Button(
            video_control_frame, 
            text="暂停", 
            command=self.toggle_video_playback,
            width=10,
            state=tk.DISABLED
        )
        self.play_pause_button.pack(side=tk.LEFT, padx=5)
        
        # 视频帧率调整
        ttk.Label(
            video_control_frame, 
            text="帧延迟(ms):",
            font=("Arial", 10)
        ).pack(side=tk.LEFT, padx=5)
        
        self.delay_var = tk.StringVar(value="100")
        delay_entry = ttk.Entry(
            video_control_frame, 
            textvariable=self.delay_var,
            width=5
        )
        delay_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            video_control_frame, 
            text="应用", 
            command=self.update_video_delay,
            width=5
        ).pack(side=tk.LEFT, padx=5)
        
        # 创建识别结果显示框架 - 只显示纯文本结果
        result_frame = ttk.Frame(main_frame)
        result_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 使用Grid布局显示结果
        self.result_labels = ttk.Label(
            result_frame, 
            text="识别结果将显示在图像上",
            font=("Arial", 10)
        )
        self.result_labels.pack(pady=5)
    
    def get_device(self):
        """检测并返回可用的设备（CPU/GPU）"""
        if torch.cuda.is_available():
            return "cuda"
        else:
            return "cpu"
    
    def load_model(self):
        """加载YOLO模型"""
        try:
            if not os.path.exists(self.model_path):
                self.status_var.set(f"错误: 模型文件不存在 - {self.model_path}")
                return False
            
            device_info = f"GPU: {torch.cuda.get_device_name(0)}" if self.device == "cuda" else "CPU"
            self.status_var.set(f"正在加载模型... 使用 {device_info}")
            
            self.model = YOLO(self.model_path)
            self.model.to(self.device)
            
            self.status_var.set(f"模型加载成功，使用 {device_info}，请选择图片")
            return True
        except Exception as e:
            self.status_var.set(f"模型加载失败: {str(e)}")
            print(f"模型加载错误详情: {str(e)}")
            return False
    
    def select_image(self):
        """选择图片文件"""
        image_file = filedialog.askopenfilename(
            title="选择加油机屏幕图片",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp"),
                ("所有文件", "*.*")
            ]
        )
        
        if not image_file:
            return
        
        try:
            # 重置部分结果，但保留ROI坐标
            self.amount_result = ""
            self.volume_result = ""
            self.price_result = ""
            self.all_detected_digits = []
            
            # 读取图像
            self.current_image = cv2.imread(image_file)
            if self.current_image is None:
                self.status_var.set("错误: 无法读取图片文件")
                return
            
            # 首先显示原始图像
            self.display_image(self.current_image)
            
            # 对整个图片进行数字识别
            device_info = "GPU" if self.device == "cuda" else "CPU"
            self.status_var.set(f"正在识别图片中的所有数字... 使用 {device_info}")
            
            # 进行全图数字识别
            self.detect_all_digits()
            
            # 启用ROI选择按钮
            self.roi_button.config(state=tk.NORMAL)
            self.video_button.config(state=tk.NORMAL)  # 允许直接选择视频
            
            # 如果已经保存了ROI，则直接使用保存的ROI进行处理
            if self.roi_saved:
                self.status_var.set("使用已保存的识别区域处理图片...")
                self.process_with_saved_roi()
            else:
                # 否则引导用户选择ROI
                self.roi_mode = "amount"
                self.start_roi_selection()
        
        except Exception as e:
            self.status_var.set(f"处理图片时出错: {str(e)}")
            print(f"错误详情: {str(e)}")
    
    def detect_all_digits(self):
        """对整个图片进行数字识别"""
        if not hasattr(self, 'model') or self.model is None:
            if not self.load_model():
                return
        
        try:
            start_time = time.time()
            
            results = self.model.predict(
                source=self.current_image,
                conf=self.conf_thres,
                iou=self.iou_thres,
                device=self.device,
                verbose=False
            )
            
            inference_time = time.time() - start_time
            
            result_image = self.current_image.copy()
            
            self.all_detected_digits = []
            for result in results:
                boxes = result.boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf = box.conf.cpu().numpy()[0]
                    cls = int(box.cls.cpu().numpy()[0])
                    
                    self.all_detected_digits.append((cls, conf, (x1 + x2) / 2, x1, y1, x2, y2))
                    
                    cv2.rectangle(result_image, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                    cv2.putText(result_image, str(cls), (int(x1), int(y1)-10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            self.display_image_with_results = result_image.copy()
            self.display_image(result_image)
            
            if not self.all_detected_digits:
                self.status_var.set("未检测到任何数字，请尝试调整图片")
        
        except Exception as e:
            self.status_var.set(f"识别过程出错: {str(e)}")
            print(f"识别错误详情: {str(e)}")
    
    def start_roi_selection(self):
        """开始ROI选择过程"""
        if self.current_image is None:
            self.status_var.set("请先选择一张图片")
            return
        
        self.selecting_roi = True
        
        # 重置ROI坐标
        self.roi_start_x = 0
        self.roi_start_y = 0
        self.roi_end_x = 0
        self.roi_end_y = 0
        
        # 删除之前的矩形框（如果有的话）
        if self.roi_rectangle_id is not None:
            self.canvas.delete(self.roi_rectangle_id)
            self.roi_rectangle_id = None
        
        # 根据当前模式设置提示信息
        if self.roi_mode == "amount":
            self.status_var.set("请框选加油金额区域")
        else:
            self.status_var.set("请框选加油升数区域")
        
        # 显示带有识别结果的图像
        if self.display_image_with_results is not None:
            self.display_image(self.display_image_with_results)
        else:
            self.display_image(self.current_image)
    
    def reset_roi(self):
        """重置ROI选择"""
        self.roi_start_x = 0
        self.roi_start_y = 0
        self.roi_end_x = 0
        self.roi_end_y = 0
        self.roi_rectangle_id = None
    
    def on_mouse_down(self, event):
        """鼠标按下事件处理"""
        if not self.selecting_roi or self.current_image is None:
            return
        
        # 记录起始位置
        self.roi_start_x = event.x
        self.roi_start_y = event.y
        
        # 删除之前的矩形
        if self.roi_rectangle_id:
            self.canvas.delete(self.roi_rectangle_id)
        
        # 创建新矩形
        self.roi_rectangle_id = self.canvas.create_rectangle(
            self.roi_start_x, self.roi_start_y, 
            self.roi_start_x, self.roi_start_y,
            outline="yellow", width=2
        )
    
    def on_mouse_move(self, event):
        """鼠标移动事件处理"""
        if not self.selecting_roi or not self.roi_rectangle_id:
            return
        
        # 更新矩形
        self.roi_end_x = event.x
        self.roi_end_y = event.y
        self.canvas.coords(
            self.roi_rectangle_id,
            self.roi_start_x, self.roi_start_y,
            self.roi_end_x, self.roi_end_y
        )
    
    def on_mouse_up(self, event):
        """鼠标释放事件处理"""
        if not self.selecting_roi or not self.roi_rectangle_id:
            return
        
        # 完成ROI选择
        self.roi_end_x = event.x
        self.roi_end_y = event.y
        
        # 确保ROI有效
        if abs(self.roi_end_x - self.roi_start_x) < 10 or abs(self.roi_end_y - self.roi_start_y) < 10:
            messagebox.showwarning("警告", "选择的区域太小，请重新选择")
            self.canvas.delete(self.roi_rectangle_id)
            self.roi_rectangle_id = None
            return
        
        # 获取画布尺寸和图像尺寸
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        img_height, img_width = self.current_image.shape[:2]
        
        # 计算图像在画布上的实际显示尺寸
        display_width = int(img_width * self.scale_factor)
        display_height = int(img_height * self.scale_factor)
        
        # 计算图像在画布上的偏移量（居中显示）
        offset_x = (canvas_width - display_width) // 2
        offset_y = (canvas_height - display_height) // 2
        
        # 调整鼠标坐标，考虑图像在画布上的偏移
        canvas_x1 = self.roi_start_x - offset_x
        canvas_y1 = self.roi_start_y - offset_y
        canvas_x2 = self.roi_end_x - offset_x
        canvas_y2 = self.roi_end_y - offset_y
        
        # 确保坐标在图像显示范围内
        canvas_x1 = max(0, min(display_width, canvas_x1))
        canvas_y1 = max(0, min(display_height, canvas_y1))
        canvas_x2 = max(0, min(display_width, canvas_x2))
        canvas_y2 = max(0, min(display_height, canvas_y2))
        
        # 转换回原始图像坐标
        x1 = int(canvas_x1 / self.scale_factor)
        y1 = int(canvas_y1 / self.scale_factor)
        x2 = int(canvas_x2 / self.scale_factor)
        y2 = int(canvas_y2 / self.scale_factor)
        
        # 确保坐标在图像范围内
        x1 = max(0, min(img_width-1, x1))
        y1 = max(0, min(img_height-1, y1))
        x2 = max(0, min(img_width-1, x2))
        y2 = max(0, min(img_height-1, y2))
        
        # 确保x1 < x2, y1 < y2
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1
        
        # 使用当前已有的识别结果图像
        if self.display_image_with_results is None:
            img_with_roi = self.current_image.copy()
        else:
            img_with_roi = self.display_image_with_results.copy()
        
        # 根据当前模式处理ROI
        if self.roi_mode == "amount":
            # 保存金额ROI坐标
            self.roi_amount_coords = (x1, y1, x2, y2)
            
            # 使用绿色矩形标记金额区域
            cv2.rectangle(img_with_roi, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 使用支持中文的函数添加文本
            img_with_roi = self.cv2_add_chinese_text(
                img_with_roi, 
                "金额", 
                (x1, y1-60),
                60,
                (0, 255, 0)
            )
            
            # 切换到加油量ROI选择模式
            self.roi_mode = "volume"
            self.selecting_roi = True
            
            # 更新状态提示
            if self.is_processing_video:
                self.status_var.set("视频已暂停，请框选加油量区域")
            else:
                self.status_var.set("请框选加油量区域")
            
        else:  # volume模式
            # 保存加油量ROI坐标
            self.roi_volume_coords = (x1, y1, x2, y2)
            
            # 使用蓝色矩形标记加油量区域
            cv2.rectangle(img_with_roi, (x1, y1), (x2, y2), (255, 0, 0), 2)
            
            # 使用支持中文的函数添加文本
            img_with_roi = self.cv2_add_chinese_text(
                img_with_roi, 
                "加油量", 
                (x1, y1-60),
                60,
                (255, 0, 0)
            )
            
            # 完成所有ROI选择
            self.selecting_roi = False
            
            # 标记ROI已保存
            self.roi_saved = True
            
            # 保存带有ROI标记的图像
            self.display_image_with_results = img_with_roi.copy()
            
            # 如果是视频模式且视频已暂停
            if self.is_processing_video and self.is_video_paused:
                self.status_var.set("识别区域已设置，可以继续播放")
                # 当视频暂停时，只需要显示带有ROI的图像，不需要立即处理结果
            else:
                # 如果是图片模式或视频正在播放，则处理ROI内的识别结果
                self.status_var.set("已选择所有识别区域，正在处理识别结果...")
                # 处理ROI内的识别结果
                self.root.after(100, self.process_roi_results)
            
            # 如果ROI已保存且不在视频模式，启用视频按钮
            if self.roi_saved and not self.is_processing_video:
                self.video_button.config(state=tk.NORMAL)
        
        # 更新显示
        self.display_image(img_with_roi)
    
    def process_roi_results(self):
        """处理ROI内的数字识别结果"""
        try:
            if self.roi_amount_coords == (0, 0, 0, 0) or self.roi_volume_coords == (0, 0, 0, 0):
                messagebox.showwarning("警告", "请先选择识别区域")
                return
            
            result_image = self.display_image_with_results.copy() if self.display_image_with_results is not None else self.current_image.copy()
            
            amount_digits = self.filter_digits_in_roi(self.roi_amount_coords)
            amount_digits.sort(key=lambda x: x[2])
            
            volume_digits = self.filter_digits_in_roi(self.roi_volume_coords)
            volume_digits.sort(key=lambda x: x[2])
            
            amount_str = ""
            for digit, _, _, _, _, _, _ in amount_digits:
                amount_str += str(digit)
            self.amount_result = amount_str
            
            volume_str = ""
            for digit, _, _, _, _, _, _ in volume_digits:
                volume_str += str(digit)
            self.volume_result = volume_str
            
            self.calculate_price()
            
            formatted_amount = self.format_amount(amount_str)
            formatted_volume = self.format_volume(volume_str)
            
            self.status_var.set(f"识别完成 - 金额: {formatted_amount}, 加油量: {formatted_volume}, 单价: {self.price_result}")
            
            self.display_image(result_image)
            
        except Exception as e:
            self.status_var.set(f"处理识别结果出错: {str(e)}")
            print(f"处理错误详情: {str(e)}")
    
    def filter_digits_in_roi(self, roi_coords):
        """从全图检测结果中筛选出在指定ROI内的数字"""
        filtered_digits = []
        x1, y1, x2, y2 = roi_coords
        
        for digit_info in self.all_detected_digits:
            digit, conf, x_center, dx1, dy1, dx2, dy2 = digit_info
            
            digit_center_x = (dx1 + dx2) / 2
            digit_center_y = (dy1 + dy2) / 2
            
            if (x1 <= digit_center_x <= x2) and (y1 <= digit_center_y <= y2):
                filtered_digits.append(digit_info)
        
        return filtered_digits
    
    def format_amount(self, digits_str):
        """格式化金额字符串"""
        if not digits_str:
            return "未检测"
        
        if len(digits_str) >= 2:
            integer_part = digits_str[:-2]
            decimal_part = digits_str[-2:]
            
            if not integer_part:
                integer_part = "0"
            
            return f"¥ {integer_part}.{decimal_part}"
        else:
            padded_amount = digits_str.zfill(2)
            return f"¥ 0.{padded_amount}"

    def format_volume(self, digits_str):
        """格式化加油量字符串"""
        if not digits_str:
            return "未检测"
        return f"{digits_str} L"

    def calculate_price(self):
        """计算单价"""
        try:
            if not self.amount_result or not self.volume_result:
                self.price_result = "无法计算"
                return
            
            amount = float(self.amount_result) / 100
            volume = float(self.volume_result) / 100
            
            if volume > 0:
                price = amount / volume
                self.price_result = f"{price:.2f}"
            else:
                self.price_result = "无法计算"
        except Exception as e:
            self.price_result = "计算错误"
            print(f"计算单价时出错: {str(e)}")

    def display_image(self, image):
        """在画布上显示图像"""
        # 转换OpenCV图像到Tkinter可显示的格式
        if image is None:
            return
            
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(image_rgb)
        
        # 调整图像大小以适应画布
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            # 如果画布尚未渲染，使用默认大小
            canvas_width = 600
            canvas_height = 400
        
        # 保持宽高比
        img_width, img_height = img.size
        ratio = min(canvas_width/img_width, canvas_height/img_height)
        new_size = (int(img_width*ratio), int(img_height*ratio))
        
        # 保存缩放比例用于ROI计算
        self.scale_factor = ratio
        
        img = img.resize(new_size, Image.LANCZOS)
        
        # 转换为PhotoImage并保存引用(防止垃圾回收)
        self.photo = ImageTk.PhotoImage(image=img)
        
        # 在画布上显示图像
        self.canvas.delete("all")
        self.canvas.create_image(
            canvas_width//2, canvas_height//2,
            image=self.photo, anchor=tk.CENTER
        )
    
    def cv2_add_chinese_text(self, img, text, position, font_size, color):
        """使用PIL在图像上添加中文文本"""
        # 将OpenCV图像转换为PIL图像
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # 创建可绘制对象
        draw = ImageDraw.Draw(img_pil)
        
        # 加载字体
        try:
            font = ImageFont.truetype(self.font_path, font_size)
        except Exception as e:
            print(f"无法加载字体: {e}")
            # 如果无法加载字体，使用默认字体
            font = ImageFont.load_default()
        
        # 绘制文本
        draw.text(position, text, font=font, fill=color)
        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)

    def select_video(self):
        """选择视频文件"""
        # 不再要求预先设置ROI - 视频可以单独设置ROI
        video_file = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv"),
                ("所有文件", "*.*")
            ]
        )
        
        if not video_file:
            return
            
        try:
            # 释放之前的视频
            self.release_video()
            
            # 打开视频文件
            self.video_capture = cv2.VideoCapture(video_file)
            
            if not self.video_capture.isOpened():
                messagebox.showerror("错误", "无法打开视频文件")
                return
                
            # 设置状态
            self.is_processing_video = True
            self.is_video_paused = False
            
            # 启用播放控制按钮
            self.play_pause_button.config(text="暂停", state=tk.NORMAL)
            
            # 更新状态
            self.status_var.set("正在播放视频...暂停后可以选择识别区域")
            
            # 开始处理视频
            self.process_video_frame()
            
        except Exception as e:
            self.status_var.set(f"处理视频时出错: {str(e)}")
            print(f"视频处理错误详情: {str(e)}")
            self.release_video()
    
    def process_video_frame(self):
        """处理视频帧"""
        if not self.is_processing_video or self.video_capture is None:
            return
            
        if self.is_video_paused:
            # 如果视频暂停，每隔一段时间检查是否恢复
            self.root.after(100, self.process_video_frame)
            return
            
        # 读取一帧
        ret, frame = self.video_capture.read()
        
        if not ret:
            # 视频结束
            self.status_var.set("视频处理完成")
            self.release_video()
            return
            
        # 处理当前帧
        self.current_image = frame.copy()
        
        # 如果已设置ROI，则对视频帧进行ROI分析
        if self.roi_saved:
            self.all_detected_digits = []
            # 识别当前帧中的数字
            self.detect_all_digits()
            
            # 应用保存的ROI进行处理
            self.process_with_saved_roi()
        else:
            # 直接显示当前帧，等待用户暂停并选择ROI
            self.display_image(self.current_image)
            # 如果没有设置ROI，状态栏提示用户
            self.status_var.set("尚未设置识别区域，请暂停视频后选择区域")
        
        # 处理下一帧
        self.root.after(self.video_delay, self.process_video_frame)
    
    def toggle_video_playback(self):
        """切换视频播放/暂停状态"""
        if not self.is_processing_video:
            return
            
        self.is_video_paused = not self.is_video_paused
        
        if self.is_video_paused:
            self.play_pause_button.config(text="继续播放")
            
            # 当视频暂停时，可以选择ROI
            if not self.roi_saved:
                self.status_var.set("视频已暂停，请框选加油金额区域")
                # 保存当前帧用于ROI选择
                if self.current_image is not None:
                    self.display_image_with_results = self.current_image.copy()
                    # 清除之前的识别结果
                    self.all_detected_digits = []
                    # 识别当前帧中的数字
                    self.detect_all_digits()
                    # 开始选择ROI
                    self.roi_mode = "amount"
                    self.start_roi_selection()
            else:
                self.status_var.set("视频已暂停")
        else:
            self.play_pause_button.config(text="暂停")
            self.status_var.set("视频继续播放")
            # 继续处理帧
            self.process_video_frame()
            
    def update_video_delay(self):
        """更新视频帧间延迟"""
        try:
            delay = int(self.delay_var.get())
            if delay < 10:
                delay = 10  # 设置最小延迟
            elif delay > 1000:
                delay = 1000  # 设置最大延迟
                
            self.video_delay = delay
            self.delay_var.set(str(delay))
            self.status_var.set(f"视频帧延迟已更新为 {delay} 毫秒")
            
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的数字")
            self.delay_var.set(str(self.video_delay))
    
    def release_video(self):
        """释放视频资源"""
        if self.video_capture is not None:
            self.video_capture.release()
            self.video_capture = None
            
        self.is_processing_video = False
        self.is_video_paused = False
        self.play_pause_button.config(text="暂停", state=tk.DISABLED)
    
    def process_with_saved_roi(self):
        """使用已保存的ROI区域处理图片"""
        try:
            # 确认已有保存的ROI区域
            if self.roi_amount_coords == (0, 0, 0, 0) or self.roi_volume_coords == (0, 0, 0, 0):
                messagebox.showwarning("警告", "没有有效的识别区域，请重新选择")
                self.start_roi_selection()
                return
            
            # 显示ROI区域
            result_image = self.display_image_with_results.copy() if self.display_image_with_results is not None else self.current_image.copy()
            
            # 在图像上标记金额ROI区域
            x1, y1, x2, y2 = self.roi_amount_coords
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            result_image = self.cv2_add_chinese_text(
                result_image, 
                "金额", 
                (x1, y1-60),
                60,
                (0, 255, 0)
            )
            
            # 在图像上标记加油量ROI区域
            x1, y1, x2, y2 = self.roi_volume_coords
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            result_image = self.cv2_add_chinese_text(
                result_image, 
                "加油量", 
                (x1, y1-60),
                60,
                (255, 0, 0)
            )
            
            # 保存带有ROI标记的图像
            self.display_image_with_results = result_image.copy()
            
            # 显示带有ROI的图像
            self.display_image(result_image)
            
            # 处理ROI内的识别结果
            self.root.after(100, self.process_roi_results)
            
        except Exception as e:
            self.status_var.set(f"处理识别结果出错: {str(e)}")
            print(f"处理错误详情: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = AmountDetectionApp(root)
    root.mainloop()

# 程序入口点
if __name__ == "__main__":
    main()






