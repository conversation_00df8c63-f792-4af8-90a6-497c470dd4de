import tkinter as tk
from tkinter import ttk, messagebox

class VerticalLineCalculator:
    """
    垂直线位置计算器
    用于计算垂直线在图像中的位置比例
    """
    def __init__(self, root):
        self.root = root
        self.root.title("垂直线位置计算器")
        self.root.geometry("700x550")
        self.root.resizable(True, True)
        
        # 创建变量
        self.image_width_px = tk.IntVar(value=1080)  # 图像x像素宽度
        self.ratio_cm_px = tk.DoubleVar(value=0.1)   # 图像cm/像素比例
        self.reference_point_px = tk.IntVar(value=0) # 定位点像素
        self.distance_to_move_cm = tk.DoubleVar(value=0) # 要移动的距离(cm)
        self.result_position_px = tk.IntVar(value=0)  # 定位后像素
        self.result_ratio = tk.DoubleVar(value=0)     # 垂直线位置比例
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        """
        创建界面组件
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="输入参数", padding="10 10 10 10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图像x像素宽度
        ttk.Label(input_frame, text="图像x像素宽度:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.image_width_px, width=15).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Label(input_frame, text="像素").grid(row=0, column=2, sticky=tk.W, pady=5)
        
        # 图像cm/像素比例
        ttk.Label(input_frame, text="图像cm/像素比例:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.ratio_cm_px, width=15).grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Label(input_frame, text="cm/像素").grid(row=1, column=2, sticky=tk.W, pady=5)
        
        # 定位点像素
        ttk.Label(input_frame, text="定位点像素:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.reference_point_px, width=15).grid(row=2, column=1, sticky=tk.W, pady=5)
        ttk.Label(input_frame, text="像素").grid(row=2, column=2, sticky=tk.W, pady=5)
        
        # 要移动的距离
        ttk.Label(input_frame, text="要移动的距离:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.distance_to_move_cm, width=15).grid(row=3, column=1, sticky=tk.W, pady=5)
        ttk.Label(input_frame, text="cm").grid(row=3, column=2, sticky=tk.W, pady=5)
        
        # 计算按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        ttk.Button(button_frame, text="计算垂直线位置", command=self.calculate).pack()
        
        # 结果区域
        result_frame = ttk.LabelFrame(main_frame, text="计算结果", padding="10 10 10 10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 定位后像素
        ttk.Label(result_frame, text="定位后像素:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(result_frame, textvariable=self.result_position_px).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Label(result_frame, text="像素").grid(row=0, column=2, sticky=tk.W, pady=5)
        
        # 垂直线位置比例
        ttk.Label(result_frame, text="垂直线位置比例:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(result_frame, textvariable=self.result_ratio).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 说明文本
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10 10 10 10")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        info_text = "在相机前方放置好标定板后，用如本相机和侧面摄像头分别采集一副2d图像。\n"
        info_text += "用侧面摄像头校正程序先打开如本2d，根据需要顺时针或者逆时针旋转，然后点击标定板左右两点，输入标定板长度，计算得到此时标定板中心和相机中心的距离差异，也就是（要移动的距离）。\n"
        info_text += "然后打开侧面摄像头2d图像，点击标定板左右，输入长度，计算得到侧面摄像头（cm像素比例），标定板中心点的像素位置（定位点像素）。\n"
        info_text += "然后在本程序总输入相应数据，计算可以得到侧面摄像头垂直线的比例。\n"
        info_text += "将垂直线比例和cm像素比例，更新到引导系统的config文件。"
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT, wraplength=600).pack(anchor=tk.W)
    
    def calculate(self):
        """
        计算垂直线位置比例
        """
        try:
            # 获取输入值
            image_width = self.image_width_px.get()
            ratio = self.ratio_cm_px.get()
            reference_point = self.reference_point_px.get()
            distance = self.distance_to_move_cm.get()
            
            # 验证输入
            if image_width <= 0:
                raise ValueError("图像宽度必须大于0")
            if ratio <= 0:
                raise ValueError("比例必须大于0")
            
            # 计算要移动的像素数
            pixels_to_move = distance / ratio
            
            # 计算定位后像素
            position_px = reference_point + pixels_to_move
            
            # 确保位置在有效范围内
            position_px = max(0, min(position_px, image_width))
            
            # 计算垂直线位置比例
            position_ratio = position_px / image_width
            
            # 更新结果
            self.result_position_px.set(round(position_px))
            self.result_ratio.set(round(position_ratio, 4))
            
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中发生错误: {str(e)}")


def main():
    root = tk.Tk()
    app = VerticalLineCalculator(root)
    root.mainloop()


if __name__ == "__main__":
    main()