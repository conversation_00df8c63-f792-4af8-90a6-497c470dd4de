import cv2
import numpy as np
import os # <-- 导入 os 模块

# 初始化变量
image_points = []  # 存储图像坐标
image = None       # 全局变量存储图像

def click_event(event, x, y, flags, param):
    """鼠标点击回调函数，记录点击位置并绘制标记"""
    global image
    if event == cv2.EVENT_LBUTTONDOWN:
        image_points.append((x, y))  # 记录坐标 (u, v)
        cv2.circle(image, (x, y), 5, (0, 0, 255), -1)  # 绘制红点
        cv2.imshow("Mark Points", image)

# 读取图像并标记点
image = cv2.imread(r"C:\Users\<USER>\Pictures\Camera Roll\calibrate.jpg")
# --- 注释掉手动标记代码 --- 
# cv2.namedWindow("Mark Points")
# cv2.setMouseCallback("Mark Points", click_event)
# 
# print("请按顺序点击地面标志点（与世界坐标文件顺序一致），按ESC退出")
# while True:
#     cv2.imshow("Mark Points", image)
#     key = cv2.waitKey(1) & 0xFF
#     if key == 27 or len(image_points) >= 24:  # 修改点数检查为 24
#         break
# 
# cv2.destroyAllWindows()
# 
# # 保存图像坐标到文件
# with open(r"F:\python code\yolov11 realtimedect\correct\image_points.txt", "w") as f:
#     for u, v in image_points:
#         f.write(f"{u} {v}\n")
# ---------------------------

# 获取脚本所在的目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 加载世界坐标（假设Z=0）
world_points = []
world_points_file_path = os.path.join(script_dir, "world_points.txt") # <-- 使用完整路径
# 指定使用 UTF-8 编码读取文件
with open(world_points_file_path, "r", encoding='utf-8') as f: 
    for line in f:
        line = line.strip() # 先去除首尾空格
        # 跳过空行和注释行
        if not line or line.startswith('#'): 
            continue
        # 现在可以安全地解析
        X, Y = map(float, line.split())
        world_points.append([X, Y, 0.0])
world_points = np.array(world_points, dtype=np.float32)  # 形状 (N, 3)

# --- 取消注释：从文件加载图像坐标 --- 
image_points_list = [] # Use a temporary list
image_points_file_path = os.path.join(script_dir, "image_points.txt") # <-- 使用完整路径
with open(image_points_file_path, "r") as f: # Use the correct path
    for line in f:
        u, v = map(float, line.strip().split())
        image_points_list.append([u, v])
image_points = np.array(image_points_list, dtype=np.float32)  # 形状 (N, 2), Overwrite the global list
# ---------------------------------

# 检查加载的点数是否足够
if len(world_points) != len(image_points):
    print(f"错误：世界坐标点数 ({len(world_points)}) 与图像坐标点数 ({len(image_points)}) 不匹配！")
    exit()
elif len(image_points) < 4: # calibrateCamera 至少需要4个点
    print(f"错误：图像坐标点数 ({len(image_points)}) 太少，至少需要4个点进行标定。")
    exit()

# 标定相机参数（内参和畸变系数）
ret, K, D, rvecs, tvecs = cv2.calibrateCamera(
    [world_points], [image_points], image.shape[:2][::-1], None, None
)

print("内参矩阵 K:\n", K)
print("畸变系数 D:\n", D)

# --- 新增：将结果写入文件 --- 
# 构造 result.txt 的完整路径
result_file_path = os.path.join(script_dir, "result.txt")

# 使用完整路径打开文件
with open(result_file_path, "w") as f: 
    f.write("内参矩阵 K:\n")
    f.write(np.array2string(K, precision=8, separator=', ')) # 使用np.array2string以获得更好的格式
    f.write("\n\n") # 添加空行
    f.write("畸变系数 D:\n")
    f.write(np.array2string(D, precision=8, separator=', '))
    f.write("\n")
# ----------------------------

# 矫正畸变
h, w = image.shape[:2]
# 计算优化后的新相机矩阵，alpha=1 保留所有像素
new_K, roi = cv2.getOptimalNewCameraMatrix(K, D, (w, h), 1, (w, h))

# 使用新的相机矩阵进行畸变矫正
undistorted_img = cv2.undistort(image, K, D, None, new_K)

# (可选) 裁剪图像到ROI区域，以去除黑色边框
# 如果你想去除黑边，可以取消下面的注释，但这会再次裁剪图像
# x, y, w_roi, h_roi = roi
# undistorted_img = undistorted_img[y:y+h_roi, x:x+w_roi]

undistorted_file_path = os.path.join(script_dir, "undistorted.jpg") # <-- 使用完整路径
cv2.imwrite(undistorted_file_path, undistorted_img)