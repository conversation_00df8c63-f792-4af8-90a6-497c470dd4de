import cv2
import torch
import numpy as np
import time
from threading import Thread
import queue
import sys
import os
from ultralytics import YOLO
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk

# 摄像头ID
CAM_ID_1 = 0
CAM_ID_2 = 1

# 检测参数
CONF_THRESHOLD = 0.25  # 置信度阈值
IOU_THRESHOLD = 0.45   # IOU阈值
IMG_SIZE = 640         # 图像大小
FPS_TARGET = 20        # 目标检测帧率

class CameraThread(Thread):
    """摄像头线程类"""
    def __init__(self, camera_id, name):
        super().__init__()
        self.camera_id = camera_id
        self.name = name
        self.frame_queue = queue.Queue(maxsize=2)  # 限制队列大小，防止内存占用过大
        self.running = False
        self.daemon = True  # 设置为守护线程
    
    def run(self):
        """线程运行函数"""
        self.running = True
        
        # 使用DirectShow后端打开摄像头
        cap = cv2.VideoCapture(self.camera_id, cv2.CAP_DSHOW)
        
        # 设置摄像头分辨率和帧率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)  # 设置宽度
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)  # 设置高度
        cap.set(cv2.CAP_PROP_FPS, 30)  # 设置帧率
        
        if not cap.isOpened():
            print(f"错误: 无法打开摄像头 {self.name} (ID: {self.camera_id})")
            return
            
        while self.running:
            ret, frame = cap.read()
            if ret:
                # 如果队列已满，移除旧帧
                if self.frame_queue.full():
                    try:
                        self.frame_queue.get_nowait()
                    except queue.Empty:
                        pass
                # 添加新帧
                try:
                    self.frame_queue.put_nowait(frame)
                except queue.Full:
                    pass
            else:
                print(f"警告: {self.name} 读取帧失败")
                break
                
        # 释放资源
        cap.release()
        print(f"{self.name} 已停止")
    
    def get_frame(self):
        """获取当前帧"""
        try:
            return self.frame_queue.get_nowait()
        except queue.Empty:
            return None
    
    def stop(self):
        """停止线程"""
        self.running = False
        # 清空队列
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except queue.Empty:
                break

class YOLODetector:
    def __init__(self, model_path, device='cuda:0'):
        print(f"正在加载模型: {model_path}")
        self.device = device
        # 检查CUDA是否可用，如果不可用则使用CPU
        if not torch.cuda.is_available() and 'cuda' in device:
            print("CUDA不可用，使用CPU进行推理")
            self.device = 'cpu'
            
        # 加载模型
        try:
            self.model = YOLO(model_path)
            print(f"模型加载成功: {model_path}")
        except Exception as e:
            print(f"加载模型失败: {e}")
            raise
    
    def detect(self, frame):
        """执行目标检测"""
        if frame is None:
            return None, []
            
        try:
            # 执行推理
            results = self.model.predict(
                source=frame,
                conf=CONF_THRESHOLD,
                iou=IOU_THRESHOLD,
                device=self.device,
                verbose=False
            )
            
            # 获取第一个结果
            result = results[0]
            
            # 获取检测框信息
            if hasattr(result, 'boxes') and len(result.boxes):
                # 获取检测框、置信度和类别
                boxes = result.boxes.cpu().numpy()
                detections = []
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0]
                    conf = box.conf[0]
                    cls = box.cls[0]
                    detections.append([x1, y1, x2, y2, conf, cls])
            else:
                detections = []
            
            # 使用YOLO的绘图功能绘制检测结果
            annotated_frame = result.plot()
            
            return annotated_frame, detections
            
        except Exception as e:
            print(f"检测过程中出错: {e}")
            return frame, []

class YOLODetectionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("YOLO双摄像头检测系统")
        self.root.geometry("1280x720")  # 缩小界面大小
        
        # 初始化变量
        self.cam1 = None
        self.cam2 = None
        self.detector1 = None
        self.detector2 = None
        self.cam_id_1 = 0
        self.cam_id_2 = 1
        # 模型路径初始化为空
        self.model_path_1 = ""
        self.model_path_2 = ""
        self.running = False
        self.detecting = False
        self.fps = 0
        self.fps_counter = 0
        self.fps_timer = time.time()
        
        # 创建主窗口布局
        self.create_control_panel()
        self.create_display_area()
        
        # 创建状态栏
        self.status_bar = tk.Label(root, text="请选择模型", bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 模型选择区域
        model_frame = ttk.LabelFrame(control_frame, text="模型选择", padding=5)
        model_frame.pack(fill=tk.X, pady=5)
        
        # 模型1选择
        ttk.Label(model_frame, text="模型1:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.model1_path_var = tk.StringVar(value="未选择模型")
        model1_label = ttk.Label(model_frame, textvariable=self.model1_path_var)
        model1_label.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Button(model_frame, text="选择模型1", command=lambda: self.select_model(1)).grid(row=0, column=2, padx=5, pady=5)
        
        # 模型2选择
        ttk.Label(model_frame, text="模型2:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.model2_path_var = tk.StringVar(value="未选择模型")
        model2_label = ttk.Label(model_frame, textvariable=self.model2_path_var)
        model2_label.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Button(model_frame, text="选择模型2", command=lambda: self.select_model(2)).grid(row=1, column=2, padx=5, pady=5)
        
        # 摄像头选择区域
        camera_frame = ttk.LabelFrame(control_frame, text="摄像头选择", padding=5)
        camera_frame.pack(fill=tk.X, pady=5)
        
        # 摄像头1选择
        ttk.Label(camera_frame, text="摄像头1:").grid(row=0, column=0, padx=5, pady=5)
        self.cam1_combo = ttk.Combobox(camera_frame, values=[0, 1, 2, 3], width=5)
        self.cam1_combo.set(CAM_ID_1)
        self.cam1_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # 摄像头2选择
        ttk.Label(camera_frame, text="摄像头2:").grid(row=0, column=2, padx=5, pady=5)
        self.cam2_combo = ttk.Combobox(camera_frame, values=[0, 1, 2, 3], width=5)
        self.cam2_combo.set(CAM_ID_2)
        self.cam2_combo.grid(row=0, column=3, padx=5, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # 开始检测按钮
        self.start_btn = ttk.Button(button_frame, text="开始检测", command=self.start_detection, state=tk.DISABLED)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止检测按钮
        self.stop_btn = ttk.Button(button_frame, text="停止检测", command=self.stop_detection, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # FPS显示
        self.fps_var = tk.StringVar(value="FPS: 0")
        ttk.Label(button_frame, textvariable=self.fps_var).pack(side=tk.RIGHT, padx=5)

    def select_model(self, model_num):
        """选择模型文件"""
        initial_dir = os.path.dirname(self.model_path_1 or self.model_path_2 or ".")
        file_path = filedialog.askopenfilename(
            title=f"选择YOLO模型 {model_num}",
            initialdir=initial_dir,
            filetypes=[
                ("PyTorch模型", "*.pt"),
                ("ONNX模型", "*.onnx"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                # 尝试加载模型以验证有效性
                YOLO(file_path)
                
                if model_num == 1:
                    self.model_path_1 = file_path
                    self.model1_path_var.set(os.path.basename(file_path))
                else:
                    self.model_path_2 = file_path
                    self.model2_path_var.set(os.path.basename(file_path))
                
                self.status_bar.config(text=f"模型{model_num}加载成功: {os.path.basename(file_path)}")
                
                # 检查是否可以启用开始按钮
                if self.model_path_1 and self.model_path_2:
                    self.start_btn.config(state=tk.NORMAL)
                    self.status_bar.config(text="两个模型都已加载，可以开始检测")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载模型失败: {str(e)}")
                self.status_bar.config(text=f"模型{model_num}加载失败")

    def toggle_detection(self):
        """切换检测状态"""
        if not self.detecting:
            # 检查是否选择了模型
            if not self.model_path_1 or not self.model_path_2:
                messagebox.showwarning("警告", "请先选择两个模型")
                return
                
            # 启动检测
            self.start_detection()
            self.start_btn.config(text="停止检测")
            self.detecting = True
        else:
            # 停止检测
            self.stop_detection()
            self.start_btn.config(text="开始检测")
            self.detecting = False
    
    def start_detection(self):
        """启动检测"""
        try:
            print("正在启动检测...")
            if not self.model_path_1 or not self.model_path_2:
                messagebox.showwarning("警告", "请先选择两个模型")
                return
                
            self.status_bar.config(text="正在加载模型...")
            print(f"加载模型1: {self.model_path_1}")
            print(f"加载模型2: {self.model_path_2}")
            
            self.detector1 = YOLODetector(self.model_path_1, device='cuda:0')
            self.detector2 = YOLODetector(self.model_path_2, device='cuda:0')
            
            self.detecting = True
            self.status_bar.config(text="检测已启动")
            print("检测已成功启动")
            
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            
        except Exception as e:
            print(f"启动检测时出错: {e}")
            self.status_bar.config(text=f"错误: {str(e)}")
            self.detecting = False
            messagebox.showerror("错误", f"加载模型失败: {str(e)}")
    
    def stop_detection(self):
        # 释放检测器
        self.detector1 = None
        self.detector2 = None
        
        self.status_bar.config(text="检测已停止")
    
    def start_cameras(self):
        self.status_bar.config(text="正在初始化摄像头...")
        
        # 获取摄像头ID
        self.cam_id_1 = int(self.cam1_combo.get())
        self.cam_id_2 = int(self.cam2_combo.get())
        
        # 初始化摄像头线程
        self.cam1 = CameraThread(self.cam_id_1, "摄像头1")
        self.cam2 = CameraThread(self.cam_id_2, "摄像头2")
        
        # 启动摄像头线程
        self.cam1.start()
        self.cam2.start()
        
        # 等待摄像头初始化
        time.sleep(2)
        
        # 启动更新循环
        self.fps_counter = 0
        self.fps_timer = time.time()
        self.running = True
        self.update_frames()
        
        self.status_bar.config(text="摄像头已启动")
    
    def stop_cameras(self):
        self.running = False
        
        # 停止摄像头线程
        if self.cam1:
            self.cam1.stop()
            self.cam1 = None
            
        if self.cam2:
            self.cam2.stop()
            self.cam2 = None
        
        # 重置显示
        self.cam1_display.config(text="摄像头1未启动", image="")
        self.cam2_display.config(text="摄像头2未启动", image="")
        
        # 保存对象引用，防止垃圾回收
        self.cam1_photo = None
        self.cam2_photo = None
        
        self.status_bar.config(text="摄像头已停止")
    
    def create_display_area(self):
        """创建显示区域"""
        # 创建显示框架
        display_frame = ttk.Frame(self.root)
        display_frame.pack(expand=True, fill=tk.BOTH, padx=10, pady=5)
        
        # 左侧摄像头显示
        cam1_frame = ttk.LabelFrame(display_frame, text="摄像头1")
        cam1_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.cam1_display = ttk.Label(cam1_frame)
        self.cam1_display.pack(expand=True, fill=tk.BOTH)
        
        # 右侧摄像头显示
        cam2_frame = ttk.LabelFrame(display_frame, text="摄像头2")
        cam2_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        self.cam2_display = ttk.Label(cam2_frame)
        self.cam2_display.pack(expand=True, fill=tk.BOTH)
        
        # 配置网格权重
        display_frame.grid_columnconfigure(0, weight=1)
        display_frame.grid_columnconfigure(1, weight=1)
        display_frame.grid_rowconfigure(0, weight=1)
        
        # 初始化显示
        self.cam1_display.config(text="等待摄像头1启动...")
        self.cam2_display.config(text="等待摄像头2启动...")
    
    def update_frames(self):
        """更新摄像头画面"""
        if not self.running:
            return
        
        # 获取摄像头1的帧
        frame1 = self.cam1.get_frame() if self.cam1 else None
        # 获取摄像头2的帧
        frame2 = self.cam2.get_frame() if self.cam2 else None
        
        # 处理摄像头1
        if frame1 is not None:
            if self.detecting and self.detector1:
                # 使用YOLO进行检测
                result_frame1, detections1 = self.detector1.detect(frame1)
                # 使用检测后的帧
                display_frame1 = result_frame1
            else:
                # 使用原始帧
                display_frame1 = frame1
            
            if display_frame1 is not None:
                # 转换为Tkinter图像并显示
                img = cv2.cvtColor(display_frame1, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(img)
                img = img.resize((640, 360), Image.LANCZOS)  # 调整大小
                self.cam1_photo = ImageTk.PhotoImage(image=img)
                self.cam1_display.config(image=self.cam1_photo, text="")
        
        # 处理摄像头2
        if frame2 is not None:
            if self.detecting and self.detector2:
                # 使用YOLO进行检测
                result_frame2, detections2 = self.detector2.detect(frame2)
                # 使用检测后的帧
                display_frame2 = result_frame2
            else:
                # 使用原始帧
                display_frame2 = frame2
            
            if display_frame2 is not None:
                # 转换为Tkinter图像并显示
                img = cv2.cvtColor(display_frame2, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(img)
                img = img.resize((640, 360), Image.LANCZOS)  # 调整大小
                self.cam2_photo = ImageTk.PhotoImage(image=img)
                self.cam2_display.config(image=self.cam2_photo, text="")
        
        # 计算和显示FPS
        self.fps_counter += 1
        if time.time() - self.fps_timer >= 1.0:
            self.fps = self.fps_counter
            self.fps_counter = 0
            self.fps_timer = time.time()
            self.fps_var.set(f"FPS: {self.fps:.1f}")
        
        # 安排下一次更新
        self.root.after(int(1000 / FPS_TARGET), self.update_frames)

def main():
    root = tk.Tk()
    app = YOLODetectionApp(root)
    
    # 启动摄像头
    root.after(1000, app.start_cameras)
    
    # 设置窗口关闭事件
    root.protocol("WM_DELETE_WINDOW", lambda: (app.stop_cameras(), root.destroy()))
    
    root.mainloop()

if __name__ == "__main__":
    main()
