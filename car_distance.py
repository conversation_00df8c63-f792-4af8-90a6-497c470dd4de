import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import os
import threading
import time
from ultralytics import YOLO
import re

class ParkingLineDetector:
    def __init__(self, root):
        """初始化界面"""
        self.root = root
        self.root.title("车位距离检测 (透视变换)")
        self.root.geometry("1200x800")
        
        # 存储当前图像和绘制状态
        self.current_image = None
        self.drawing_image = None
        
        # YOLO模型相关
        self.model = None
        self.model_path = "F:/python code/yolomodel/328best11.pt"  # 设置默认模型路径
        self.detection_results = None
        self.tire_boxes = []
        
        # 透视变换相关
        self.H = None # Perspective Transform Matrix
        self.ground_width = None # Real-world ground plane width (from calibration)
        self.ground_height = None # Real-world ground plane height (from calibration)
        self.calibration_file = None # Path to loaded calibration file
        self.default_calibration_path = "F:/python code/yolov11 realtimedect/distortion/720p.txt" # Default calibration file
        self.calibration_img_points = None # Store img_points from calibration file
        self.calib_marker_ids = [] # Store canvas IDs for calibration markers
        
        # 视频处理相关
        self.video_path = ""
        self.video_capture = None
        self.video_frame = None
        self.is_video = False
        self.video_playing = False
        self.video_thread = None
        self.video_output = None
        self.video_fps = 0
        self.video_frame_count = 0
        self.current_frame_index = 0
        
        # 创建界面
        self.create_ui()
        
        # 自动加载默认模型
        self.load_default_model()
        # 尝试自动加载默认标定文件
        self.load_default_calibration_data()
    
    def load_default_model(self):
        """加载默认YOLO模型"""
        try:
            if os.path.exists(self.model_path):
                self.model = YOLO(self.model_path)
                self.model_path_var.set(self.model_path)
                self.status_var.set(f"默认模型加载成功: {os.path.basename(self.model_path)}")
            else:
                self.status_var.set("默认模型文件不存在，请手动选择模型")
        except Exception as e:
            self.status_var.set(f"加载默认模型出错: {str(e)}")
    
    def load_default_calibration_data(self):
        """尝试加载默认的标定文件"""
        if os.path.exists(self.default_calibration_path):
            self.status_var.set(f"尝试加载默认标定文件: {os.path.basename(self.default_calibration_path)}")
            self.load_calibration_data(calib_file=self.default_calibration_path)
        else:
            self.status_var.set("未找到默认标定文件，请手动加载。")
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 模型选择区域
        model_frame = ttk.Frame(control_frame)
        model_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(model_frame, text="YOLO模型:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.model_path_var = tk.StringVar(value=self.model_path)
        ttk.Entry(model_frame, textvariable=self.model_path_var, width=60).grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(model_frame, text="选择模型", command=self.select_model).grid(row=0, column=2, padx=5)
        
        # 标定数据加载区域
        calib_frame = ttk.Frame(control_frame)
        calib_frame.pack(fill=tk.X, pady=5)
        ttk.Label(calib_frame, text="标定数据:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.calib_path_var = tk.StringVar(value="未加载")
        ttk.Entry(calib_frame, textvariable=self.calib_path_var, width=60, state='readonly').grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(calib_frame, text="加载标定", command=self.load_calibration_data).grid(row=0, column=2, padx=5)
        
        # 文件选择区域
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="视频文件:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=70).grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(file_frame, text="选择视频", command=self.select_video).grid(row=0, column=2, padx=5)
        ttk.Button(file_frame, text="选择图片", command=self.select_image_file).grid(row=0, column=3, padx=5)
        
        # 处理按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 添加YOLO检测按钮
        self.detect_button = ttk.Button(button_frame, text="检测轮胎", command=self.detect_tires)
        self.detect_button.pack(side=tk.LEFT, padx=5)
        
        # 添加计算距离按钮 (Requires calibration)
        self.calculate_button = ttk.Button(button_frame, text="计算距离", command=self.calculate_distance, state=tk.DISABLED)
        self.calculate_button.pack(side=tk.LEFT, padx=5)
        
        # 添加视频处理按钮 (Requires calibration)
        self.process_video_button = ttk.Button(button_frame, text="处理整个视频", command=self.process_video, state=tk.DISABLED)
        self.process_video_button.pack(side=tk.LEFT, padx=5)
        
        # 添加保存结果按钮
        self.save_button = ttk.Button(button_frame, text="保存当前帧", command=self.save_result)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # 视频控制区域
        video_control_frame = ttk.Frame(control_frame)
        video_control_frame.pack(fill=tk.X, pady=5)
        
        # 视频进度条
        self.video_progress_var = tk.DoubleVar(value=0)
        self.video_progress = ttk.Scale(video_control_frame, from_=0, to=100, 
                                       orient=tk.HORIZONTAL, variable=self.video_progress_var,
                                       command=self.on_video_progress_change)
        self.video_progress.pack(fill=tk.X, padx=5, pady=5)
        self.video_progress.config(state=tk.DISABLED)
        
        # 视频控制按钮
        video_buttons_frame = ttk.Frame(video_control_frame)
        video_buttons_frame.pack(fill=tk.X, padx=5)
        
        self.video_play_button = ttk.Button(video_buttons_frame, text="播放", command=self.play_video)
        self.video_play_button.pack(side=tk.LEFT, padx=5)
        self.video_play_button.config(state=tk.DISABLED)
        
        self.video_pause_button = ttk.Button(video_buttons_frame, text="暂停", command=self.pause_video)
        self.video_pause_button.pack(side=tk.LEFT, padx=5)
        self.video_pause_button.config(state=tk.DISABLED)
        
        self.video_stop_button = ttk.Button(video_buttons_frame, text="停止", command=self.stop_video)
        self.video_stop_button.pack(side=tk.LEFT, padx=5)
        self.video_stop_button.config(state=tk.DISABLED)
        
        # 帧信息显示
        self.frame_info_var = tk.StringVar(value="")
        ttk.Label(video_control_frame, textvariable=self.frame_info_var).pack(side=tk.RIGHT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(control_frame, textvariable=self.status_var).pack(anchor=tk.W, pady=5)
        
        # 创建图像显示区域
        image_display_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图像画布
        self.image_canvas = tk.Canvas(image_display_frame, bg="black")
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 结果信息显示
        self.result_info_var = tk.StringVar(value="")
        ttk.Label(image_display_frame, textvariable=self.result_info_var, font=("Arial", 12, "bold")).pack(anchor=tk.CENTER, pady=5)
    
    def select_model(self):
        """选择YOLO模型文件"""
        model_file = filedialog.askopenfilename(
            title="选择YOLO模型文件",
            filetypes=[
                ("PyTorch模型", "*.pt"),
                ("ONNX模型", "*.onnx"),
                ("所有文件", "*.*")
            ]
        )
        if model_file:
            self.model_path = model_file
            self.model_path_var.set(model_file)
            self.status_var.set(f"已选择模型: {os.path.basename(model_file)}")
            
            # 尝试加载模型
            try:
                self.model = YOLO(model_file)
                self.status_var.set(f"模型加载成功: {os.path.basename(model_file)}")
            except Exception as e:
                messagebox.showerror("错误", f"加载模型时出错: {str(e)}")
                self.status_var.set(f"错误: {str(e)}")
    
    def load_calibration_data(self, calib_file=None):
        """从 .txt 文件加载包含H矩阵和地面尺寸的标定数据
        
        Args:
            calib_file (str, optional): 要加载的标定文件路径。
                                        如果为 None (通常由按钮点击触发)，
                                        则尝试加载默认标定文件。
        """
        # 如果是由按钮点击触发 (calib_file is None)，弹出文件选择对话框
        if calib_file is None:
             # --- Define the initial directory --- 
             initial_dir = "F:/python code/yolov11 realtimedect/distortion" 
             # Check if the directory exists, otherwise default to current dir or user's choice
             if not os.path.isdir(initial_dir):
                  print(f"[Warning] Initial directory not found: {initial_dir}. Using default.")
                  initial_dir = "." # Or some other fallback

             # --- Re-introduce the file dialog prompt with initialdir --- 
             calib_file = filedialog.askopenfilename(
                 title="选择标定数据文件 (.txt)",
                 initialdir=initial_dir, # Set the initial directory
                 filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
             )
             if not calib_file:
                 return # 用户取消选择

        # Proceed with loading from the determined calib_file path
        try:
            # --- 读取和解析文本文件 --- #
            data_dict = {}
            current_key = None
            value_lines = []

            with open(calib_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    # 跳过空行和注释行
                    if not line or line.startswith('#'):
                        continue

                    # 检测是否是新的键值对（以':'结尾，且不是矩阵/数组行）
                    if ':' in line and not line.startswith('['):
                        # 保存上一个键的值
                        if current_key and value_lines:
                            data_dict[current_key] = "\n".join(value_lines).strip()
                        
                        # 分割键和值的第一部分
                        key_part, value_part = line.split(':', 1)
                        current_key = key_part.strip()
                        # 初始化当前键的值列表
                        value_lines = [value_part.strip()] if value_part.strip() else []
                    # 如果是当前键的值的一部分（例如矩阵的多行）
                    elif current_key:
                         value_lines.append(line)
                
                # 保存最后一个键的值
                if current_key and value_lines:
                    data_dict[current_key] = "\n".join(value_lines).strip()

            # --- 提取和转换数据 --- #
            # 提取基础参数
            try:
                # .get() 提供默认值以防键不存在
                ground_width_loaded = float(data_dict.get('ground_width', 'NaN'))
                ground_height_loaded = float(data_dict.get('ground_height', 'NaN'))
                # scale_ratio 在这个文件里可能不需要，但解析出来也无妨
                # scale_ratio_str = data_dict.get('scale_ratio') or data_dict.get('scale_ratio (at time of point marking/import)')
                # scale_ratio_saved = float(scale_ratio_str if scale_ratio_str else 'NaN')
            except ValueError as e:
                raise ValueError(f"解析基础参数 (ground_width/height) 时出错: {e}")

            # 解析 H 矩阵字符串
            h_str = data_dict.get('H (Perspective Transform Matrix)')
            if not h_str:
                 raise ValueError("未在文件中找到 H (Perspective Transform Matrix) 数据")
            try:
                 # 使用正则表达式查找所有数字 (浮点数，包括科学计数法)
                 numbers = re.findall(r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?", h_str)
                 # 检查是否找到了9个数字 (3x3矩阵)
                 if len(numbers) == 9:
                     # 将数字列表转换为3x3的NumPy数组
                     H_loaded = np.array([float(n) for n in numbers]).reshape((3, 3)).astype(np.float32)
                 else:
                     raise ValueError(f"无法从字符串解析 3x3 H 矩阵 (找到 {len(numbers)} 个数字，需要 9 个)")
            except Exception as e:
                raise ValueError(f"解析 H 矩阵时出错: {e}\n原始字符串:\n{h_str}")

            # --- Parse img_points string from calibration file --- #
            points_str = data_dict.get('img_points (Original Image Coordinates)')
            if not points_str:
                 # Optional: Make img_points optional in calib file? For now, require it.
                 raise ValueError("未在文件中找到 img_points (Original Image Coordinates) 数据")
            try:
                numbers = re.findall(r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?", points_str)
                if len(numbers) == 8: # Expect 4 points * 2 coordinates = 8 numbers
                     calib_img_points_loaded = np.array([float(n) for n in numbers]).reshape((4, 2)).astype(np.float32)
                else:
                     raise ValueError(f"无法从字符串解析 4x2 img_points (找到 {len(numbers)} 个数字，需要 8 个)")
            except Exception as e:
                raise ValueError(f"解析 img_points 时出错: {e}\n原始字符串:\n{points_str}")

            # --- 验证数据 --- #
            if H_loaded.shape != (3, 3):
                 # 这一步理论上不会触发，因为上面已经检查了数字数量和reshape
                 raise ValueError("解析得到的 H 矩阵形状不正确 (应为 3x3)。")
            # Also validate the parsed img_points shape
            if calib_img_points_loaded.shape != (4, 2):
                 raise ValueError("解析得到的 img_points 形状不正确 (应为 4x2)。")
            if not isinstance(ground_width_loaded, (int, float)) or ground_width_loaded <= 0:
                raise ValueError("解析得到的地面宽度无效。")
            if not isinstance(ground_height_loaded, (int, float)) or ground_height_loaded <= 0:
                raise ValueError("解析得到的地面高度无效。")

            # --- 存储加载的数据 --- #
            self.H = H_loaded
            self.ground_width = ground_width_loaded
            self.ground_height = ground_height_loaded
            self.calibration_img_points = calib_img_points_loaded # Store the loaded points
            self.calibration_file = calib_file # 保存文件名

            # --- 更新UI --- #
            self.calib_path_var.set(os.path.basename(calib_file)) # 显示文件名
            self.status_var.set(f"标定数据加载成功: {os.path.basename(calib_file)}")

            # 如果模型也已加载，则启用相关按钮
            if self.model is not None:
                # Enable calculate_button only after detection now
                # self.calculate_button.config(state=tk.NORMAL)
                if self.is_video: # 只有加载了视频才启用视频处理
                    self.process_video_button.config(state=tk.NORMAL)
                else: # 如果是图片模式，则禁用视频处理按钮
                    self.process_video_button.config(state=tk.DISABLED)
                    # --- Draw calibration rectangle if in image mode --- 
                    if self.current_image is not None and self.calibration_img_points is not None:
                         try:
                              # Calculate ratio and draw
                              h, w = self.current_image.shape[:2]
                              canvas_width = self.image_canvas.winfo_width()
                              canvas_height = self.image_canvas.winfo_height()
                              if canvas_width <= 1: canvas_width = 800 # Fallback
                              if canvas_height <= 1: canvas_height = 600
                              img_ratio = min(canvas_width / w, canvas_height / h)

                              canvas_calib_points = []
                              for x_orig, y_orig in self.calibration_img_points:
                                   canvas_x = x_orig * img_ratio
                                   canvas_y = y_orig * img_ratio
                                   canvas_calib_points.append((canvas_x, canvas_y))

                              new_w, new_h = int(w * img_ratio), int(h * img_ratio)
                              offset_x = (canvas_width - new_w) // 2
                              offset_y = (canvas_height - new_h) // 2
                              final_canvas_points = [(pt[0] + offset_x, pt[1] + offset_y) for pt in canvas_calib_points]

                              self.draw_calibration_rectangle(final_canvas_points)
                              self.status_var.set(f"标定数据加载成功: {os.path.basename(calib_file)}. 校准框已绘制.")
                         except Exception as draw_err:
                              print(f"[Error] Failed to draw calibration rectangle after load: {draw_err}")
                              self.status_var.set(f"标定数据加载成功，但绘制校准框失败: {draw_err}")

        except FileNotFoundError:
             self.status_var.set(f"标定文件加载失败: 文件未找到 {os.path.basename(calib_file)}")
             messagebox.showerror("加载失败", f"文件未找到:\n{calib_file}")
             self.H = None # 出错时重置
             self.video_stop_button.config(state=tk.DISABLED)
             self.frame_info_var.set("")

             # 重置计算/处理按钮状态 (依赖后续加载)
             self.detect_button.config(state=tk.DISABLED) # Disable detect button too
             self.calculate_button.config(state=tk.DISABLED)
             self.process_video_button.config(state=tk.DISABLED)
        except Exception as e:
            self.status_var.set(f"加载标定数据失败: {e}")
            messagebox.showerror("加载失败", f"无法加载标定数据:\n{str(e)}")
            self.H = None # 出错时重置
            self.video_stop_button.config(state=tk.DISABLED)
            self.frame_info_var.set("")
            self.detect_button.config(state=tk.DISABLED)
            self.calculate_button.config(state=tk.DISABLED)
            self.process_video_button.config(state=tk.DISABLED)
    
    def select_video(self):
        """选择待处理的视频文件"""
        video_file = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv"),
                ("所有文件", "*.*")
            ]
        )
        if video_file:
            self.file_path_var.set(video_file)
            self.video_path = video_file
            self.is_video = True
            self.status_var.set(f"已选择视频: {os.path.basename(video_file)}")
            
            # 加载视频第一帧
            self.load_video_first_frame(video_file)
    
    def load_video_first_frame(self, video_path):
        """加载视频的第一帧并显示"""
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError("无法打开视频文件")
            
            # 获取视频信息
            self.video_fps = cap.get(cv2.CAP_PROP_FPS)
            self.video_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 读取第一帧
            ret, frame = cap.read()
            if not ret:
                raise ValueError("无法读取视频帧")
            
            # 存储当前帧
            self.current_image = frame
            self.drawing_image = frame.copy()
            self.video_frame = frame
            self.current_frame_index = 0
            
            # 转换为RGB用于显示
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 显示在图像画布上
            self.display_image(frame_rgb)
            
            # 清空检测状态
            self.tire_boxes = []
            
            # 更新状态
            self.result_info_var.set("请加载标定数据并检测轮胎")
            self.frame_info_var.set(f"帧: 1/{self.video_frame_count} | FPS: {self.video_fps:.2f}")
            
            # 启用视频进度条和控制按钮
            self.video_progress.config(state=tk.NORMAL)
            self.video_progress_var.set(0)
            self.video_play_button.config(state=tk.NORMAL)
            
            # Check if calibration is loaded to enable process_video button
            if self.H is not None and self.model is not None:
                self.process_video_button.config(state=tk.NORMAL)
            else:
                self.process_video_button.config(state=tk.DISABLED)
            
            # 关闭视频捕获
            cap.release()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载视频时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def display_image(self, image):
        """在画布上显示图像"""
        # 获取画布尺寸
        canvas_width = self.image_canvas.winfo_width()
        canvas_height = self.image_canvas.winfo_height()
        
        # 如果画布尚未渲染，使用默认尺寸
        if canvas_width <= 1:
            canvas_width = 800
        if canvas_height <= 1:
            canvas_height = 600
        
        # 调整图像大小以适应画布
        h, w = image.shape[:2]
        ratio = min(canvas_width/w, canvas_height/h)
        new_size = (int(w*ratio), int(h*ratio))
        
        # 调整图像大小
        resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_AREA)
        
        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        
        # 转换为PhotoImage
        tk_image = ImageTk.PhotoImage(image=pil_image)
        
        # 保存引用，防止垃圾回收
        self.image_canvas.image = tk_image
        
        # 清除画布并显示图像
        self.image_canvas.delete("all")
        self.image_canvas.create_image(canvas_width//2, canvas_height//2, image=tk_image)
    
    def detect_tires(self):
        """使用YOLO模型检测图像中的轮胎"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择一个视频文件")
            return
        
        if self.model is None:
            messagebox.showwarning("警告", "请先选择YOLO模型")
            return
        
        try:
            # 更新状态
            self.status_var.set("正在检测轮胎...")
            self.root.update_idletasks()
            
            # 使用YOLO模型进行检测
            results = self.model(self.current_image, conf=0.25)
            self.detection_results = results[0]
            
            # 创建结果图像 for drawing
            self.drawing_image = self.current_image.copy()
            
            # 找到所有轮胎
            self.tire_boxes = []
            
            # 处理检测结果
            if self.detection_results.boxes:
                for box in self.detection_results.boxes:
                    # 获取类别
                    cls = int(box.cls[0])
                    cls_name = self.detection_results.names[cls]
                    conf = float(box.conf[0])
                    
                    # 只处理轮胎
                    if cls_name != "tire":
                        continue
                    
                    # 获取边界框
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    self.tire_boxes.append((x1, y1, x2, y2))
                    
                    # 绘制边界框
                    color = (0, 255, 0) # Green for tires
                    cv2.rectangle(self.drawing_image, (x1, y1), (x2, y2), color, 2)
                    
                    # 添加标签
                    label = f"{cls_name} {conf:.2f}"
                    cv2.putText(self.drawing_image, label, (x1, y1 - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 显示结果图像
            result_rgb = cv2.cvtColor(self.drawing_image, cv2.COLOR_BGR2RGB)
            self.display_image(result_rgb)
            
            # 更新状态
            if self.tire_boxes:
                self.status_var.set(f"检测到 {len(self.tire_boxes)} 个轮胎. 请加载标定数据计算距离." if self.H is None else f"检测到 {len(self.tire_boxes)} 个轮胎. 点击 '计算距离' 获取坐标.")
                self.result_info_var.set(f"检测到 {len(self.tire_boxes)} 个轮胎")
                # Enable calculate button only if calibration is also loaded AND tires were found
                if self.H is not None:
                    self.calculate_button.config(state=tk.NORMAL)
                else:
                     self.calculate_button.config(state=tk.DISABLED)
            else:
                self.status_var.set("未检测到轮胎")
                self.result_info_var.set("未检测到轮胎")
                self.calculate_button.config(state=tk.DISABLED) # Disable if no tires
        
        except Exception as e:
            messagebox.showerror("错误", f"检测轮胎时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def calculate_distance(self):
        """
        使用透视变换计算轮胎底部中心点的实际地面坐标 (X, Y)，
        假设标定原点为左下角，X向右为正，Y向上为正。
        """
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择一个视频文件")
            return

        if not self.tire_boxes:
            messagebox.showwarning("警告", "请先运行轮胎检测")
            return

        if self.H is None: # 检查H矩阵是否已加载
            messagebox.showwarning("警告", "请先加载标定数据 (.npz 文件)")
            return
        # ground_height 理论上不再直接用于距离计算，但加载进来是好的实践
        if self.ground_height is None:
             messagebox.showwarning("警告", "加载的标定数据缺少 ground_height")
             # 可以选择继续，但最好是要求数据完整
             # return

        try:
            self.status_var.set("正在计算实际坐标...")
            self.root.update_idletasks()

            # 从原始图像开始绘制，避免重复绘制
            self.drawing_image = self.current_image.copy()

            tire_ground_coords = [] # 存储每个轮胎的地面坐标

            for i, tire_box in enumerate(self.tire_boxes):
                x1, y1, x2, y2 = tire_box

                # 1. 绘制轮胎边界框 (在干净的图像上)
                cv2.rectangle(self.drawing_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 2. 获取轮胎底边中点的像素坐标
                tire_pix_x = int((x1 + x2) / 2)
                tire_pix_y = int(y2) # Y坐标取底边的值

                # 3. 在图像上标记这个像素点 (可选，用于调试)
                cv2.circle(self.drawing_image, (tire_pix_x, tire_pix_y), 5, (255, 0, 255), -1) # 品红色圆点

                # 4. 准备透视变换所需的格式
                pixel_point = np.array([[[tire_pix_x, tire_pix_y]]], dtype=np.float32)

                # 5. 应用透视变换 H
                ground_point = cv2.perspectiveTransform(pixel_point, self.H)
                real_x, real_y = ground_point[0][0]

                # 6. Calculate distances to Left (X) and Bottom (B) edges
                dist_left = real_x
                dist_bottom = self.ground_height - real_y

                tire_ground_coords.append({
                    "id": i + 1,
                    "dist_left": dist_left,
                    "dist_bottom": dist_bottom
                })

                # 7. 在图像上显示计算出的地面距离
                # 假设单位是米 (m)
                coord_text = f"T{i+1}: (L:{dist_left:.2f}m, B:{dist_bottom:.2f}m)" # Changed Y to B
                text_x_pos = x1
                text_y_pos = y1 - 10 if y1 > 10 else y1 + 15 # 放在框上方或下方
                cv2.putText(self.drawing_image, coord_text, (text_x_pos, text_y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2) # 青色文字

            # --- 更新UI --- 
            # 在下方的结果标签中显示总结信息
            if tire_ground_coords:
                summary_text = " | ".join([f"T{d['id']}:(L:{d['dist_left']:.2f}m, B:{d['dist_bottom']:.2f}m)" for d in tire_ground_coords]) # Changed Y to B
                self.result_info_var.set(f"轮胎距左/底边距离: {summary_text}") # Updated label text
                self.status_var.set("距离计算完成 (基于透视变换)") # Changed status text
            else:
                self.result_info_var.set("未能计算轮胎距离")

            # 更新画布上的图像显示
            result_rgb = cv2.cvtColor(self.drawing_image, cv2.COLOR_BGR2RGB)
            self.display_image(result_rgb)

        except Exception as e:
            messagebox.showerror("错误", f"计算坐标时出错: {str(e)}")
            self.status_var.set(f"计算坐标时出错: {str(e)}")
    
    def save_result(self):
        """保存处理结果"""
        if self.drawing_image is None:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        try:
            # 选择保存路径
            save_path = filedialog.asksaveasfilename(
                title="保存结果图像",
                defaultextension=".jpg",
                filetypes=[
                    ("JPEG图像", "*.jpg"),
                    ("PNG图像", "*.png"),
                    ("所有文件", "*.*")
                ]
            )
            
            if save_path:
                # 保存图像
                cv2.imwrite(save_path, self.drawing_image)
                self.status_var.set(f"结果已保存至: {os.path.basename(save_path)}")
                messagebox.showinfo("成功", f"结果图像已保存至:\n{save_path}")
        
        except Exception as e:
            messagebox.showerror("错误", f"保存图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def on_video_progress_change(self, value):
        """处理视频进度条变化"""
        if not self.is_video or self.video_capture is None:
            return
        
        # 暂停视频播放
        was_playing = self.video_playing
        if was_playing:
            self.pause_video()
        
        # 计算新的帧位置
        new_frame_index = int(float(value) / 100 * self.video_frame_count)
        
        # 设置视频位置
        self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, new_frame_index)
        
        # 读取新帧
        ret, frame = self.video_capture.read()
        if ret:
            self.current_frame_index = new_frame_index
            self.video_frame = frame
            self.current_image = frame
            
            # 显示帧
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            self.display_image(frame_rgb)
            
            # 更新帧信息
            self.frame_info_var.set(f"帧: {self.current_frame_index+1}/{self.video_frame_count} | FPS: {self.video_fps:.2f}")
            
            # 如果之前在播放，则继续播放
            if was_playing:
                self.play_video()
        else:
            self.status_var.set("无法读取视频帧")
    
    def play_video(self):
        """播放视频"""
        if not self.is_video:
            return
        
        # 如果视频捕获未初始化，则初始化
        if self.video_capture is None:
            try:
                self.video_capture = cv2.VideoCapture(self.video_path)
                if not self.video_capture.isOpened():
                    raise ValueError("无法打开视频文件")
                
                # 设置到当前帧位置
                if self.current_frame_index > 0:
                    self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame_index)
            except Exception as e:
                messagebox.showerror("错误", f"播放视频时出错: {str(e)}")
                self.status_var.set(f"错误: {str(e)}")
                return
        
        # 设置播放状态
        self.video_playing = True
        
        # 更新按钮状态
        self.video_play_button.config(state=tk.DISABLED)
        self.video_pause_button.config(state=tk.NORMAL)
        self.video_stop_button.config(state=tk.NORMAL)
        
        # 更新状态
        self.status_var.set("正在播放视频...")
        
        # 启动播放线程
        if self.video_thread is None or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.video_playback_thread)
            self.video_thread.daemon = True
            self.video_thread.start()
    
    def video_playback_thread(self):
        """视频播放线程"""
        try:
            while self.video_playing and self.video_capture is not None:
                # 读取下一帧
                ret, frame = self.video_capture.read()
                
                if not ret:
                    # 到达视频末尾，停止播放
                    self.root.after(0, self.stop_video)
                    break
                
                # 更新当前帧
                self.current_frame_index += 1
                self.video_frame = frame
                self.current_image = frame
                
                # 更新进度条
                progress = (self.current_frame_index / self.video_frame_count) * 100
                self.root.after(0, lambda p=progress: self.video_progress_var.set(p))
                
                # 更新帧信息
                self.root.after(0, lambda idx=self.current_frame_index: 
                               self.frame_info_var.set(f"帧: {idx+1}/{self.video_frame_count} | FPS: {self.video_fps:.2f}"))
                
                # 显示帧
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                self.root.after(0, lambda img=frame_rgb: self.display_image(img))
                
                # 控制播放速度
                time.sleep(1 / self.video_fps)
        
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"播放视频时出错: {str(e)}"))
    
    def pause_video(self):
        """暂停视频播放"""
        if not self.is_video:
            return
        
        # 设置暂停状态
        self.video_playing = False
        
        # 更新按钮状态
        self.video_play_button.config(state=tk.NORMAL)
        self.video_pause_button.config(state=tk.DISABLED)
        
        # 更新状态
        self.status_var.set("视频已暂停")
    
    def stop_video(self):
        """停止视频播放"""
        if not self.is_video:
            return
        
        # 设置停止状态
        self.video_playing = False
        
        # 关闭视频捕获
        if self.video_capture is not None:
            self.video_capture.release()
            self.video_capture = None
        
        # 重置到第一帧
        if self.video_path: # Check if path exists
            self.load_video_first_frame(self.video_path)
        
        # 更新按钮状态
        self.video_play_button.config(state=tk.NORMAL if self.video_path else tk.DISABLED)
        self.video_pause_button.config(state=tk.DISABLED)
        self.video_stop_button.config(state=tk.DISABLED)
        
        # 更新状态
        self.status_var.set("视频已停止" if self.video_path else "就绪")
    
    def process_video(self):
        """处理整个视频"""
        if not self.is_video:
            messagebox.showwarning("警告", "请先选择一个视频文件")
            return
        
        if self.model is None:
            messagebox.showwarning("警告", "请先选择YOLO模型")
            return
        
        if self.manual_line is None:
            messagebox.showwarning("警告", "请先绘制停车边线")
            return
        
        try:
            # 选择保存路径
            save_path = filedialog.asksaveasfilename(
                title="保存处理后的视频",
                defaultextension=".mp4",
                filetypes=[
                    ("MP4视频", "*.mp4"),
                    ("AVI视频", "*.avi"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not save_path:
                return
            
            # 停止当前播放
            self.stop_video()
            
            # 更新状态
            self.status_var.set("正在处理视频...")
            
            # 禁用所有按钮
            self.disable_all_buttons()
            
            # 启动处理线程
            process_thread = threading.Thread(target=self.video_processing_thread, args=(save_path,))
            process_thread.daemon = True
            process_thread.start()
        
        except Exception as e:
            messagebox.showerror("错误", f"处理视频时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            self.enable_all_buttons()
    
    def disable_all_buttons(self):
        """禁用所有按钮"""
        for widget in self.root.winfo_children():
            self.disable_buttons_in_widget(widget)
    
    def enable_all_buttons(self):
        """启用所有按钮"""
        for widget in self.root.winfo_children():
            self.enable_buttons_in_widget(widget)
    
    def disable_buttons_in_widget(self, widget):
        """递归禁用控件中的所有按钮"""
        if isinstance(widget, ttk.Button) or isinstance(widget, tk.Button):
            widget.config(state=tk.DISABLED)
        
        for child in widget.winfo_children():
            self.disable_buttons_in_widget(child)
    
    def enable_buttons_in_widget(self, widget):
        """递归启用控件中的所有按钮"""
        if isinstance(widget, ttk.Button) or isinstance(widget, tk.Button):
            widget.config(state=tk.NORMAL)
        
        for child in widget.winfo_children():
            self.enable_buttons_in_widget(child)
    
    def video_processing_thread(self, save_path):
        """视频处理线程"""
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(self.video_path)
            if not cap.isOpened():
                raise ValueError("无法打开视频文件")
            
            # 获取视频信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(save_path, fourcc, fps, (width, height))
            
            # 获取边线参数
            x1, y1, x2, y2 = self.manual_line
            
            # 直线方程参数: ax + by + c = 0
            a = y2 - y1
            b = x1 - x2
            c = x2 * y1 - x1 * y2
            
            # 处理每一帧
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                
                # 更新进度
                progress = (frame_count / total_frames) * 100
                self.root.after(0, lambda p=progress: self.video_progress_var.set(p))
                self.root.after(0, lambda fc=frame_count: 
                               self.frame_info_var.set(f"处理中: {fc}/{total_frames} | {progress:.1f}%"))
                
                # 使用YOLO模型进行检测
                results = self.model(frame, conf=0.25)
                detection_results = results[0]
                
                # 创建结果图像
                result_frame = frame.copy()
                
                # 绘制边线
                cv2.line(result_frame, (x1, y1), (x2, y2), (255, 0, 0), 3)
                
                # 找到所有轮胎
                tire_boxes = []
                
                # 处理检测结果
                if detection_results.boxes:
                    for box in detection_results.boxes:
                        # 获取类别
                        cls = int(box.cls[0])
                        cls_name = detection_results.names[cls]
                        conf = float(box.conf[0])
                        
                        # 跳过car类别
                        if cls_name == "car":
                            continue
                        
                        # 获取边界框
                        box_x1, box_y1, box_x2, box_y2 = map(int, box.xyxy[0])
                        
                        # 如果是轮胎类别，存储轮胎边界框
                        if cls_name == "tire":
                            tire_boxes.append((box_x1, box_y1, box_x2, box_y2))
                        
                        # 绘制边界框
                        color = (0, 255, 0) if cls_name == "tire" else (255, 0, 0)
                        cv2.rectangle(result_frame, (box_x1, box_y1), (box_x2, box_y2), color, 2)
                        
                        # 添加标签
                        label = f"{cls_name} {conf:.2f}"
                        cv2.putText(result_frame, label, (box_x1, box_y1 - 10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                
                # 计算每个轮胎到边线的距离
                for i, tire_box in enumerate(tire_boxes):
                    # 轮胎底部中心点
                    tire_bottom_x = int((tire_box[0] + tire_box[2]) / 2)
                    tire_bottom_y = int(tire_box[3])
                    
                    # 计算点到直线的距离
                    pixel_distance = abs(a * tire_bottom_x + b * tire_bottom_y + c) / ((a**2 + b**2)**0.5)
                    
                    # 转换为实际距离（厘米）
                    real_distance = pixel_distance * self.pixels_to_cm
                    
                    # 计算投影点坐标
                    if b == 0:  # 避免除以零
                        proj_x = tire_bottom_x
                        proj_y = -c / a
                    else:
                        proj_x = (b * b * tire_bottom_x - a * b * tire_bottom_y - a * c) / (a * a + b * b)
                        proj_y = (-a * b * tire_bottom_x + a * a * tire_bottom_y - b * c) / (a * a + b * b)
                    
                    # 绘制距离线
                    cv2.line(result_frame, 
                            (tire_bottom_x, tire_bottom_y), 
                            (int(proj_x), int(proj_y)), 
                            (0, 0, 255), 2)
                    
                    # 显示距离文本
                    cv2.putText(result_frame, 
                               f"轮胎{i+1}: {real_distance:.1f}cm", 
                               (tire_box[0], tire_box[1] - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                # 写入帧
                out.write(result_frame)
                
                # 显示当前处理的帧
                if frame_count % 10 == 0:  # 每10帧更新一次显示，减少UI负担
                    result_rgb = cv2.cvtColor(result_frame, cv2.COLOR_BGR2RGB)
                    self.root.after(0, lambda img=result_rgb: self.display_image(img))
            
            # 释放资源
            cap.release()
            out.release()
            
            # 更新状态
            self.root.after(0, lambda: self.status_var.set(f"视频处理完成，已保存至: {os.path.basename(save_path)}"))
            self.root.after(0, lambda: messagebox.showinfo("成功", f"视频处理完成，已保存至:\n{save_path}"))
            
            # 重新启用按钮
            self.root.after(0, self.enable_all_buttons)
            
            # 重新加载视频
            self.root.after(0, lambda: self.load_video_first_frame(self.video_path))
        
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理视频时出错: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set(f"错误: {str(e)}"))
            self.root.after(0, self.enable_all_buttons)

    def select_image_file(self):
        """选择并加载单个图像文件"""
        # --- Define the initial directory --- 
        initial_dir = "C:/Users/<USER>/Pictures/Camera Roll"
        # Check if the directory exists, otherwise default
        if not os.path.isdir(initial_dir):
            print(f"[Warning] Initial image directory not found: {initial_dir}. Using default.")
            initial_dir = "."

        image_file = filedialog.askopenfilename(
            title="选择图片文件",
            initialdir=initial_dir, # Set the initial directory
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if not image_file:
            return

        try:
            # 读取图像
            img = cv2.imread(image_file)
            if img is None:
                raise ValueError("无法读取图像文件")

            # --- 重置状态 (针对新媒体加载) ---
            self.reset_for_new_media()
            self.is_video = False # 明确设置为非视频模式
            self.file_path_var.set(image_file) # 显示图片路径
            self.video_path = "" # 清空视频路径

            # 存储图像
            self.current_image = img
            self.drawing_image = img.copy()

            # 显示图像
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            self.display_image(img_rgb)

            # 更新状态和按钮
            self.status_var.set(f"已加载图片: {os.path.basename(image_file)}. 点击 '检测轮胎' 进行检测.") # Changed status
            self.detect_button.config(state=tk.NORMAL) # Enable detect button
            self.disable_video_controls() # 禁用视频相关控件
            self.process_video_button.config(state=tk.DISABLED) # 不能处理单张图片为视频
            self.root.update_idletasks()

            # --- Removed drawing calibration rectangle on image load --- 
            # # Draw calibration rectangle if data exists
            # if self.calibration_img_points is not None:
            #     # Need to calculate ratio specific to this image display
            #     h, w = img_rgb.shape[:2]
            #     canvas_width = self.image_canvas.winfo_width()
            #     canvas_height = self.image_canvas.winfo_height()
            #     if canvas_width <= 1: canvas_width = 800 # Default size fallback
            #     if canvas_height <= 1: canvas_height = 600
            #     img_ratio = min(canvas_width/w, canvas_height/h)
            # 
            #     # Calculate canvas coordinates for the calibration points
            #     canvas_calib_points = []
            #     for x_orig, y_orig in self.calibration_img_points:
            #         # Apply the same ratio used for displaying the image
            #         canvas_x = x_orig * img_ratio
            #         canvas_y = y_orig * img_ratio
            #         canvas_calib_points.append((canvas_x, canvas_y))
            #     
            #     # Adjust for potential centering offset if display_image centers
            #     # Assuming display_image centers, calculate offsets
            #     # This needs to match precisely how display_image places the image
            #     new_w, new_h = int(w * img_ratio), int(h * img_ratio)
            #     offset_x = (canvas_width - new_w) // 2
            #     offset_y = (canvas_height - new_h) // 2
            # 
            #     final_canvas_points = [(pt[0] + offset_x, pt[1] + offset_y) for pt in canvas_calib_points]
            # 
            #     self.draw_calibration_rectangle(final_canvas_points)

            # --- Removed automatic detection and calculation --- 
            # # 自动执行检测
            # self.detect_tires()
            # 
            # # 如果标定数据已加载，自动计算距离
            # if self.H is not None and self.tire_boxes: # Check for tires too
            #     self.status_var.set(f"轮胎检测完成. 正在计算坐标...")
            #     self.root.update_idletasks()
            #     self.calculate_distance()
            # elif self.H is None:
            #      self.status_var.set(f"轮胎检测完成. 请加载标定数据以计算坐标.")

        except Exception as e:
            messagebox.showerror("错误", f"加载图片时出错: {str(e)}")
            self.status_var.set(f"加载图片错误: {str(e)}")
            self.reset_for_new_media() # 出错时也重置状态

    def reset_for_new_media(self):
        """为加载新视频或图片重置状态"""
        # 停止可能在运行的视频
        if self.video_playing:
             self.pause_video()
        if self.video_capture is not None:
            self.video_capture.release()
            self.video_capture = None
        # 停止线程?

        # 清除检测和计算结果
        self.tire_boxes = []
        self.detection_results = None
        self.calibration_img_points = None # Reset calibration points
        self.result_info_var.set("")
        # self.drawing_image = None # Don't clear here, loaded after reset
        # self.current_image = None

        # Clear calibration markers from canvas
        self.image_canvas.delete("calibration_markers")
        self.calib_marker_ids = []

        # 重置视频UI
        self.video_progress_var.set(0)
        self.video_progress.config(state=tk.DISABLED)
        self.video_play_button.config(state=tk.DISABLED)
        self.video_pause_button.config(state=tk.DISABLED)
        self.video_stop_button.config(state=tk.DISABLED)
        self.frame_info_var.set("")

        # 重置计算/处理按钮状态 (依赖后续加载)
        self.detect_button.config(state=tk.DISABLED) # Disable detect button too
        self.calculate_button.config(state=tk.DISABLED)
        self.process_video_button.config(state=tk.DISABLED)

    def disable_video_controls(self):
        """禁用视频播放相关的控件"""
        self.video_progress.config(state=tk.DISABLED)
        self.video_play_button.config(state=tk.DISABLED)
        self.video_pause_button.config(state=tk.DISABLED)
        self.video_stop_button.config(state=tk.DISABLED)
        # 通常处理整个视频按钮也应禁用
        # self.process_video_button.config(state=tk.DISABLED)

    def draw_calibration_rectangle(self, canvas_points):
        """在画布上绘制标定时使用的矩形框和角点"""
        if len(canvas_points) != 4:
            print("[Error] draw_calibration_rectangle needs exactly 4 canvas points.")
            return

        # Clear previous markers first (although reset should handle this)
        self.image_canvas.delete("calibration_markers")
        self.calib_marker_ids = []

        # Draw points (e.g., small blue circles) and numbers
        for i, point in enumerate(canvas_points):
            x, y = point
            oval_id = self.image_canvas.create_oval(
                x - 4, y - 4, x + 4, y + 4,
                fill='cyan', outline='white', tags="calibration_markers"
            )
            text_id = self.image_canvas.create_text(
                x, y - 10, text=str(i + 1),
                fill='cyan', tags="calibration_markers"
            )
            self.calib_marker_ids.extend([oval_id, text_id])

        # Draw connecting lines (e.g., blue lines)
        points_to_connect = canvas_points + [canvas_points[0]] # Close the loop
        for i in range(4):
            line_id = self.image_canvas.create_line(
                points_to_connect[i][0], points_to_connect[i][1],
                points_to_connect[i+1][0], points_to_connect[i+1][1],
                fill='blue', width=2, tags="calibration_markers"
            )
            self.calib_marker_ids.append(line_id)
        
        print(f"[Debug] Drew calibration rectangle with {len(self.calib_marker_ids)} items.")


def main():
    """主函数"""
    root = tk.Tk()
    app = ParkingLineDetector(root)
    root.mainloop()

# 如果直接运行此脚本
if __name__ == "__main__":
    main()