import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk
from PIL import Image, ImageTk

class GroundDistanceCalculator:
    """
    地面距离计算器类
    用于计算图像中点到参考边的实际距离，以及两点之间的实际距离
    基于透视变换原理，将图像坐标映射到实际地面坐标
    """
    def __init__(self):
        # 初始化相机参数(需通过标定获取实际值)
        # 相机矩阵包含焦距和光学中心信息
        self.camera_matrix = np.array([[1000, 0, 640], [0, 1000, 360], [0, 0, 1]]) 
        # 畸变系数，用于校正镜头畸变
        self.dist_coeffs = np.array([-0.3, 0.1, 0, 0])
        
        # 地面矩形实际尺寸(7m×4m)
        self.ground_width = 7  # 水平方向长度（左右方向）
        self.ground_height = 4  # 垂直方向宽度（上下方向）
        
        # 图像控制点和地面控制点
        self.img_points = None  # 将在用户标记后设置
        # 地面控制点定义了实际坐标系统
        # 确保地面控制点正确对应实际尺寸：左右7米，上下4米
        self.ground_points = np.array([
            [0, 0],                              # 左上角 - 原点
            [self.ground_width, 0],              # 右上角 - 水平方向7米
            [self.ground_width, self.ground_height],  # 右下角 - 水平7米，垂直4米
            [0, self.ground_height]              # 左下角 - 垂直方向4米
        ], dtype=np.float32)
        
        # 透视变换矩阵 - 用于将图像坐标转换为地面实际坐标
        self.H = None
        
    def load_image(self, img_path):
        """
        加载图像（暂时去掉畸变矫正）
        参数:
            img_path: 图像文件路径
        返回:
            原始图像（不进行畸变矫正）
        """
        # 读取原始图像
        self.original_img = cv2.imread(img_path)
        if self.original_img is None:
            raise ValueError("无法加载图像")
            
        # 暂时跳过畸变矫正步骤，直接使用原始图像
        self.undistorted_img = self.original_img.copy()
        
        return self.undistorted_img
        
    def set_image_points(self, points, scale_ratio=1.0):
        """
        设置图像控制点(四个角点)
        参数:
            points: 界面上标记的四个点坐标
            scale_ratio: 图像缩放比例，用于将界面坐标转换回原始图像坐标
        """
        if len(points) != 4:
            raise ValueError("需要4个控制点")
            
        # 将画布坐标转换为原始图像坐标
        # 因为界面上显示的图像可能经过了缩放
        original_points = []
        for x, y in points:
            original_x = int(x / scale_ratio)
            original_y = int(y / scale_ratio)
            original_points.append((original_x, original_y))
            
        self.img_points = np.array(original_points, dtype=np.float32)
        
        # 计算透视变换矩阵
        # 这个矩阵将图像坐标映射到地面实际坐标
        self.H = cv2.getPerspectiveTransform(
            self.img_points,  # 源坐标（图像中的四个点）
            self.ground_points  # 目标坐标（地面实际坐标系中的四个点）
        )
    
    def calculate_distance(self, x_pixel, y_pixel):
        """
        计算点到各边的距离
        参数:
            x_pixel, y_pixel: 图像中点的坐标
        返回:
            包含到左边和底边距离的字典
        """
        if self.H is None:
            raise ValueError("请先设置图像控制点")
            
        # 将点坐标转换为适合透视变换的格式
        point = np.array([[[x_pixel, y_pixel]]], dtype=np.float32)
        # 应用透视变换，将图像坐标转换为地面实际坐标
        ground_point = cv2.perspectiveTransform(point, self.H)
        x, y = ground_point[0][0]
        
        # 计算点到左边和底边的实际距离
        return {
            "left": x,  # 水平方向距离左边的距离
            "bottom": self.ground_height - y  # 垂直方向距离底边的距离
        }
        
    def visualize(self, point=None):
        """
        可视化结果
        参数:
            point: 要测量的点坐标
        返回:
            可视化后的图像
        """
        if self.undistorted_img is None:
            raise ValueError("请先加载图像")
            
        # 复制图像以便绘制
        img = self.undistorted_img.copy()
        
        # 绘制控制点
        if self.img_points is not None:
            for pt in self.img_points:
                cv2.circle(img, tuple(pt.astype(int)), 10, (0,255,0), -1)
                
        # 绘制指定点及其距离线
        if point is not None:
            x, y = point
            # 绘制测量点
            cv2.circle(img, (int(x), int(y)), 8, (0,0,255), -1)
            
            # 计算并显示距离
            dist = self.calculate_distance(x, y)
            cv2.putText(img, f"左: {dist['left']:.2f}m", (10,30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
            cv2.putText(img, f"下: {dist['bottom']:.2f}m", (10,70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
            
            # 绘制距离线 - 确保与计算方法一致
            cv2.line(img, (int(x), int(y)), (0, int(y)), (255, 0, 0), 2)  # 左边线
            cv2.line(img, (int(x), int(y)), (int(x), img.shape[0]), (255, 0, 0), 2)  # 下边线
        
        return img


class GroundDistanceApp:
    """
    地面距离测量应用类
    提供图形界面，允许用户加载图像、标记控制点、测量距离
    """
    def __init__(self, root):
        """
        初始化应用
        参数:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("地面距离测量系统")
        
        # 创建计算器实例
        self.calculator = GroundDistanceCalculator()
        
        # 初始化测量相关属性
        self.measure_marker = None  # 测量点的界面标记
        self.measure_point = None   # 测量点的坐标
        self.measure_button = None  # 测量按钮
        
        # 初始化两点测距相关属性
        self.two_points_mode = False  # 两点测距模式标志
        self.two_points = []          # 两点测距的点坐标
        self.two_points_markers = []  # 两点测距的界面标记
        
        # 创建界面布局
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建顶部容器框架，用于并排放置控制面板和矩阵显示
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 控制面板 - 放在左侧
        control_frame = ttk.LabelFrame(top_frame, text="控制面板")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5), pady=5)
        
        # 图像选择按钮
        ttk.Button(control_frame, text="选择图像", command=self.load_image).pack(pady=5)
        
        # 添加开始测量按钮
        self.measure_button = ttk.Button(control_frame, text="开始测量", 
                                      command=self.start_measurement, state=tk.DISABLED)
        self.measure_button.pack(pady=5)
        
        # 添加两点测距按钮
        self.two_points_button = ttk.Button(control_frame, text="两点测距", 
                                         command=self.toggle_two_points_mode, state=tk.DISABLED)
        self.two_points_button.pack(pady=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(control_frame, textvariable=self.status_var).pack(pady=5)
        
        # 添加透视变换矩阵显示区域 - 放在右侧
        self.matrix_frame = ttk.LabelFrame(top_frame, text="透视变换矩阵")
        self.matrix_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        
        # 创建矩阵显示文本框 - 增加高度和宽度
        self.matrix_text = tk.Text(self.matrix_frame, height=10, width=50, font=('Courier', 10))
        self.matrix_text.pack(padx=5, pady=5, fill=tk.BOTH, expand=True)
        self.matrix_text.config(state=tk.DISABLED)  # 初始设为只读
        
        # 图像显示区域
        self.image_frame = ttk.LabelFrame(self.root, text="图像显示")
        self.image_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建画布用于显示图像和交互
        self.canvas = tk.Canvas(self.image_frame)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定点击事件处理
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        
        # 存储点击点和标记
        self.click_points = []  # 控制点坐标
        self.point_markers = [] # 控制点界面标记
        
        # 结果展示区域
        self.result_frame = ttk.LabelFrame(self.root, text="测量结果")
        self.result_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 结果显示标签
        self.result_var = tk.StringVar()
        ttk.Label(self.result_frame, textvariable=self.result_var).pack()
    def toggle_two_points_mode(self):
        """切换两点测距模式"""
        self.two_points_mode = not self.two_points_mode
        
        # 清除之前的两点测距点和标记
        self.two_points = []
        for marker in self.two_points_markers:
            self.canvas.delete(marker)
        self.two_points_markers = []
        
        # 更新按钮文本和状态提示
        if self.two_points_mode:
            self.two_points_button.config(text="取消两点测距")
            self.status_var.set("两点测距模式：请在矩形内点击第一个点")
        else:
            self.two_points_button.config(text="两点测距")
            self.status_var.set("已退出两点测距模式")
            
    def handle_two_points_click(self, event):
        """
        处理两点测距模式下的点击
        记录点坐标、绘制标记和连线，计算距离
        """
        canvas_x, canvas_y = event.x, event.y
        
        # 如果已经有两个点，清除之前的点和标记
        if len(self.two_points) == 2:
            self.two_points = []
            for marker in self.two_points_markers:
                self.canvas.delete(marker)
            self.two_points_markers = []
            
        # 添加新点
        self.two_points.append((canvas_x, canvas_y))
        
        # 绘制点标记
        marker = self.canvas.create_oval(
            event.x-5, event.y-5, event.x+5, event.y+5,
            fill='yellow', outline='black'
        )
        self.two_points_markers.append(marker)
        
        # 显示点序号
        text_marker = self.canvas.create_text(
            event.x, event.y-15,
            text=f"P{len(self.two_points)}",
            fill='yellow'
        )
        self.two_points_markers.append(text_marker)
        
        # 如果有两个点，绘制连线并计算距离
        if len(self.two_points) == 2:
            # 绘制连线
            line_marker = self.canvas.create_line(
                self.two_points[0][0], self.two_points[0][1],
                self.two_points[1][0], self.two_points[1][1],
                fill='yellow', width=2
            )
            self.two_points_markers.append(line_marker)
            
            # 计算两点之间的距离
            self.calculate_two_points_distance()
            
    def calculate_two_points_distance(self):
        """
        计算两点之间的实际距离
        将界面坐标转换为原始图像坐标，再转换为地面实际坐标，计算欧氏距离
        """
        if len(self.two_points) != 2:
            return
            
        # 转换为原始图像坐标（去除缩放影响）
        original_points = []
        for x, y in self.two_points:
            original_x = int(x / self.scale_ratio)
            original_y = int(y / self.scale_ratio)
            original_points.append((original_x, original_y))
            
        # 计算地面实际坐标
        ground_points = []
        for x, y in original_points:
            # 将点坐标转换为适合透视变换的格式
            point = np.array([[[x, y]]], dtype=np.float32)
            # 应用透视变换，将图像坐标转换为地面实际坐标
            ground_point = cv2.perspectiveTransform(point, self.calculator.H)
            ground_points.append(ground_point[0][0])
            
        # 计算欧氏距离
        x1, y1 = ground_points[0]
        x2, y2 = ground_points[1]
        distance = np.sqrt((x2-x1)**2 + (y2-y1)**2)
        
        # 输出调试信息
        print(f"地面坐标点1: ({x1:.2f}, {y1:.2f})")
        print(f"地面坐标点2: ({x2:.2f}, {y2:.2f})")
        print(f"计算距离: {distance:.2f}m")
        
        # 显示结果
        result_text = f"两点之间的距离: {distance:.2f}m"
        self.result_var.set(result_text)
        
        # 在连线中点显示距离
        mid_x = (self.two_points[0][0] + self.two_points[1][0]) / 2
        mid_y = (self.two_points[0][1] + self.two_points[1][1]) / 2
        text_marker = self.canvas.create_text(
            mid_x, mid_y-10,
            text=f"{distance:.2f}m",
            fill='white', font=('Arial', 10, 'bold')
        )
        self.two_points_markers.append(text_marker)
        
        # 更新状态
        self.status_var.set("两点测距完成")

    def draw_rectangle(self):
        """
        绘制矩形边框
        连接四个控制点形成闭合矩形
        """
        # 绘制四条边，闭合矩形
        points = self.click_points + [self.click_points[0]]  # 添加第一个点以闭合矩形
        for i in range(4):
            self.canvas.create_line(
                points[i][0], points[i][1], 
                points[i+1][0], points[i+1][1],
                fill='green', width=2
            )
        
        # 如果有测量点，绘制测量点
        if self.measure_point:
            x, y = self.measure_point
            self.canvas.create_oval(
                x-5, y-5, x+5, y+5,
                fill='blue', outline='white'
            )

    def start_measurement(self):
        """
        开始测量
        计算测量点到参考边的距离并显示结果
        """
        if self.measure_point:
            # 转换为原始图像坐标（去除缩放影响）
            original_x = int(self.measure_point[0] / self.scale_ratio)
            original_y = int(self.measure_point[1] / self.scale_ratio)
            
            # 计算距离并显示结果
            self.show_result((original_x, original_y))
            # 更新状态
            self.status_var.set("测量完成")
            # 禁用测量按钮
            self.measure_button.config(state=tk.DISABLED)
            
    def show_result(self, point):
        """
        显示测量结果
        计算距离并更新界面显示
        """
        # 计算并显示距离
        distances = self.calculator.calculate_distance(*point)
        result_text = (
            f"到左边线: {distances['left']:.2f}m\n"
            f"到底边: {distances['bottom']:.2f}m"
        )
        self.result_var.set(result_text)
        
        # 可视化结果
        vis_img = self.calculator.visualize(point)
        vis_img_rgb = cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB)
        
        # 使用与load_image相同的缩放比例
        h, w = vis_img_rgb.shape[:2]
        new_size = (int(w*self.scale_ratio), int(h*self.scale_ratio))
        resized_img = cv2.resize(vis_img_rgb, new_size, interpolation=cv2.INTER_AREA)
        
        # 转换为PIL图像并显示
        self.pil_img = Image.fromarray(resized_img)
        self.display_img = ImageTk.PhotoImage(image=self.pil_img)
        
        # 更新画布尺寸并显示图像
        self.canvas.config(width=new_size[0], height=new_size[1])
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_img)
        
        # 重新绘制控制点和测量点
        self.redraw_points()
        
    def redraw_points(self):
        """
        重新绘制控制点和测量点
        在更新图像后恢复所有标记
        """
        # 绘制控制点和序号
        for i, point in enumerate(self.click_points):
            x, y = point
            self.canvas.create_oval(
                x-5, y-5, x+5, y+5,
                fill='red', outline='white'
            )
            self.canvas.create_text(
                x, y-15,
                text=str(i+1),
                fill='white'
            )
            
        # 绘制矩形边框
        points = self.click_points + [self.click_points[0]]
        for i in range(4):
            self.canvas.create_line(
                points[i][0], points[i][1], 
                points[i+1][0], points[i+1][1],
                fill='green', width=2
            )
            
        # 绘制测量点
        if self.measure_point:
            x, y = self.measure_point
            self.canvas.create_oval(
                x-5, y-5, x+5, y+5,
                fill='blue', outline='white'
            )
            
        # 重绘两点测距点和线
        if len(self.two_points) > 0:
            for i, point in enumerate(self.two_points):
                x, y = point
                self.canvas.create_oval(
                    x-5, y-5, x+5, y+5,
                    fill='yellow', outline='black'
                )
                self.canvas.create_text(
                    x, y-15,
                    text=f"P{i+1}",
                    fill='yellow'
                )
                
            # 如果有两个点，绘制连线
            if len(self.two_points) == 2:
                self.canvas.create_line(
                    self.two_points[0][0], self.two_points[0][1],
                    self.two_points[1][0], self.two_points[1][1],
                    fill='yellow', width=2
                )

    def on_canvas_click(self, event):
        """
        处理画布点击事件
        根据当前状态执行不同操作：标记控制点、选择测量点或两点测距
        """
        if not hasattr(self, 'pil_img'):
            return  # 如果没有加载图像，不处理点击
            
        # 如果在两点测距模式下且已设置控制点
        if self.two_points_mode and len(self.click_points) == 4:
            self.handle_two_points_click(event)
            return
            
        # 如果已经收集了4个控制点，则记录测量点
        if len(self.click_points) == 4:
            # 清除之前的测量点
            if self.measure_marker:
                self.canvas.delete(self.measure_marker)
                
            # 记录测量点坐标
            canvas_x, canvas_y = event.x, event.y
            self.measure_point = (canvas_x, canvas_y)
            
            # 绘制测量点标记
            self.measure_marker = self.canvas.create_oval(
                event.x-5, event.y-5, event.x+5, event.y+5,
                fill='blue', outline='white'
            )
            
            # 启用测量按钮
            self.measure_button.config(state=tk.NORMAL)
            self.status_var.set("已选择测量点，请点击'开始测量'按钮")
            return
            
        # 记录控制点坐标
        canvas_x, canvas_y = event.x, event.y
        self.click_points.append((canvas_x, canvas_y))
        
        # 在画布上标记控制点
        marker = self.canvas.create_oval(
            event.x-5, event.y-5, event.x+5, event.y+5,
            fill='red', outline='white'
        )
        self.point_markers.append(marker)
        
        # 显示控制点序号
        self.canvas.create_text(
            event.x, event.y-15,
            text=str(len(self.click_points)),
            fill='white'
        )
        
        # 如果收集了4个控制点，设置控制点并绘制矩形
        if len(self.click_points) == 4:
            self.calculator.set_image_points(self.click_points, self.scale_ratio)
            self.draw_rectangle()
            self.status_var.set("已设置4个控制点，请点击测量点")
            self.two_points_button.config(state=tk.NORMAL)
            
            # 显示透视变换矩阵
            self.display_transform_matrix()

    def load_image(self):
        """加载图像并显示在界面上"""
        # 清除之前的点和标记
        self.click_points = []
        self.two_points = []
        for marker in self.point_markers:
            self.canvas.delete(marker)
        self.point_markers = []
        for marker in self.two_points_markers:
            self.canvas.delete(marker)
        self.two_points_markers = []
        
        # 选择图像文件
        file_path = filedialog.askopenfilename(
            filetypes=[("图像文件", "*.jpg *.jpeg *.png")]
        )
        if not file_path:
            return
            
        try:
            # 加载并矫正图像
            img = self.calculator.load_image(file_path)
            
            # 转换为RGB格式（OpenCV使用BGR，而PIL使用RGB）
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # 计算缩放比例 (最大宽度800，最大高度600)
            max_width, max_height = 800, 600
            h, w = img_rgb.shape[:2]
            self.scale_ratio = min(max_width/w, max_height/h)
            new_size = (int(w*self.scale_ratio), int(h*self.scale_ratio))
            
            # 调整图像大小以适应界面
            resized_img = cv2.resize(img_rgb, new_size, interpolation=cv2.INTER_AREA)
            
            # 转换为PIL图像
            self.pil_img = Image.fromarray(resized_img)
            
            # 转换为PhotoImage以便在Tkinter中显示
            self.display_img = ImageTk.PhotoImage(image=self.pil_img)
            
            # 显示图像在画布上
            self.canvas.config(width=new_size[0], height=new_size[1])
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_img)
            
            # 更新状态
            self.status_var.set(f"已加载图像: {file_path}")
            
        except Exception as e:
            self.status_var.set(f"错误: {str(e)}")

    def display_transform_matrix(self):
        """
        在界面上显示透视变换矩阵
        格式化矩阵以便于阅读
        """
        if self.calculator.H is None:
            return
            
        # 启用文本框编辑
        self.matrix_text.config(state=tk.NORMAL)
        
        # 清除之前的内容
        self.matrix_text.delete(1.0, tk.END)
        
        # 格式化矩阵并插入文本框
        matrix = self.calculator.H
        matrix_str = "透视变换矩阵 H:\n"
        for row in matrix:
            row_str = " ".join([f"{val:10.6f}" for val in row])
            matrix_str += row_str + "\n"
            
        self.matrix_text.insert(tk.END, matrix_str)
        
        # 禁用文本框编辑（只读模式）
        self.matrix_text.config(state=tk.DISABLED)

# 主程序入口
if __name__ == "__main__":
    root = tk.Tk()
    app = GroundDistanceApp(root)
    root.mainloop()

  