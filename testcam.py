import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time

class CameraApp:
    def __init__(self, root):
        self.root = root
        self.root.title("摄像头测试")
        self.root.geometry("800x600")

        # 存储每个摄像头的BooleanVar
        self.camera_vars = [tk.BooleanVar(value=False) for _ in range(9)] 
        
        # 存储每个摄像头的状态: {index: {cap: VideoCapture, thread: Thread, latest_frame: frame, lock: Lock, image_item_id: id}}
        self.cameras = {}

        self.is_capturing = False
        # self.photo = None # 不再需要单个photo引用，每个摄像头有自己的
        # self.image_item_id = None # 不再需要单个item ID
        
        # 用于控制UI更新的定时器ID
        self.after_id = None

        self.create_ui()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_ui(self):
        # 控制框架
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)

        # 摄像头选择复选框
        check_frame = ttk.LabelFrame(control_frame, text="选择摄像头", padding="5")
        check_frame.pack(side=tk.LEFT, padx=5)
        for i in range(9):
            ttk.Checkbutton(check_frame, text=str(i), variable=self.camera_vars[i]).pack(side=tk.LEFT, padx=2)

        # 打开摄像头按钮
        ttk.Button(control_frame, text="打开摄像头", command=self.open_selected_cameras).pack(side=tk.LEFT, padx=10)
        
        # 关闭摄像头按钮
        ttk.Button(control_frame, text="关闭摄像头", command=self.stop_all_captures).pack(side=tk.LEFT, padx=10)

        # 视频显示区域
        self.video_canvas = tk.Canvas(self.root, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 初始时在canvas中心显示文本提示
        self.show_message("选择摄像头并点击 '打开摄像头'")
        
    def open_selected_cameras(self):
        # 先停止所有当前捕获
        self.stop_all_captures()
        
        selected_indices = [i for i, var in enumerate(self.camera_vars) if var.get()]
        
        if not selected_indices:
            self.show_message("请选择至少一个摄像头")
            return
            
        self.is_capturing = True
        self.clear_canvas() # 清空初始提示文本
        
        # 预设每个摄像头的显示位置 (简化的网格布局)
        # 根据选中的摄像头数量动态计算布局会更复杂，这里先固定为最多6个位置
        canvas_width = self.video_canvas.winfo_width()
        canvas_height = self.video_canvas.winfo_height()
        if canvas_width < 1 or canvas_height < 1:
             # 如果画布尺寸无效，稍后重试或显示错误
             print("警告: 画布尺寸无效，稍后将尝试获取")
             # 暂时显示一个消息
             self.show_error("无法获取画布尺寸，请调整窗口")
             self.is_capturing = False
             return
             
        # 简化的布局: 2x3 网格
        rows = 2
        cols = 3
        cell_width = canvas_width // cols
        cell_height = canvas_height // rows

        successfully_opened_count = 0
        
        for index in selected_indices:
            print(f"尝试打开摄像头 {index}...")
            cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)
            
            if not cap or not cap.isOpened():
                print(f"无法打开摄像头 {index}")
                self.show_error(f"无法打开摄像头 {index}")
                continue
                
            print(f"成功打开摄像头 {index} (DSHOW)")
            successfully_opened_count += 1
            
            # 读取第一帧以创建image item
            ret, frame = cap.read()
            if ret:
                 # 将OpenCV图像转换为PIL图像
                 img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                 
                 # 调整图像大小以适应 cell 大小
                 if cell_width > 1 and cell_height > 1:
                     img_width, img_height = img.size
                     if img_width > 0 and img_height > 0:
                         ratio = min(cell_width / img_width, cell_height / img_height)
                         new_size = (int(img_width * ratio), int(img_height * ratio))
                         img = img.resize(new_size, Image.BILINEAR)

                 # 转换为Tkinter PhotoImage
                 # 需要将PhotoImage保存在一个不会被垃圾回收的地方，这里保存在camera状态字典里
                 photo = ImageTk.PhotoImage(image=img)

                 # 计算该摄像头在canvas上的显示位置 (中心点)
                 row_idx = (successfully_opened_count - 1) // cols
                 col_idx = (successfully_opened_count - 1) % cols
                 center_x = col_idx * cell_width + cell_width // 2
                 center_y = row_idx * cell_height + cell_height // 2

                 # 创建图像项并保存ID
                 image_item_id = self.video_canvas.create_image(
                     center_x, center_y, 
                     image=photo, 
                     anchor=tk.CENTER
                 )

                 # 存储摄像头状态
                 self.cameras[index] = {
                     'cap': cap,
                     'lock': threading.Lock(),
                     'latest_frame': frame.copy(), # 保存第一帧
                     'image_item_id': image_item_id,
                     'photo': photo, # 存储PhotoImage引用
                     'display_pos': (center_x, center_y), # 存储显示位置
                     'display_size': (cell_width, cell_height) # 存储显示区域大小
                 }

                 # 启动捕获线程
                 capture_thread = threading.Thread(target=self.capture_loop, args=(index,)) # 传递摄像头索引
                 capture_thread.daemon = True
                 capture_thread.start()
                 self.cameras[index]['thread'] = capture_thread

            else:
                print(f"无法从摄像头 {index} 读取第一帧")
                self.show_error(f"无法从摄像头 {index} 读取第一帧")
                cap.release()
                continue # 跳过当前摄像头，继续尝试打开下一个
                
        if successfully_opened_count > 0:
             # 启动定时UI更新
             self.schedule_ui_update()
        else:
             self.is_capturing = False
             self.show_error("未能成功打开任何摄像头")

    # 捕获线程循环，每个线程负责一个摄像头
    def capture_loop(self, index):
        # 从self.cameras字典中获取当前线程负责的摄像头对象
        camera_state = self.cameras.get(index)
        if not camera_state or not camera_state['cap'] or not camera_state['cap'].isOpened():
             print(f"错误: 摄像头 {index} 的捕获线程无法访问摄像头。")
             # 标记该摄像头为非活动状态 (可选，取决于错误处理粒度)
             # del self.cameras[index] # 直接删除可能导致迭代问题
             return
             
        cap = camera_state['cap']
        frame_lock = camera_state['lock']
        
        print(f"摄像头 {index} 捕获线程启动")
        while self.is_capturing and cap.isOpened():
            ret, frame = cap.read()
            if ret:
                # 将最新帧保存到共享变量，使用锁保护
                with frame_lock:
                    camera_state['latest_frame'] = frame.copy()
            else:
                # 如果无法读取帧，可能摄像头断开，停止该摄像头的捕获
                print(f"警告: 无法从摄像头 {index} 读取帧，停止捕获。")
                # 需要一种方式通知主线程处理这个摄像头的中断
                # 简单的做法是退出循环，并在主线程的更新函数中检查状态
                break
        
        print(f"摄像头 {index} 捕获线程退出")
        # 线程退出后，需要清理该摄像头的状态，但这应该由主线程或stop函数协调
        # 避免在工作线程中直接修改self.cameras字典

    # 定时UI更新函数
    def schedule_ui_update(self):
        # 如果停止捕获，则取消定时更新
        if not self.is_capturing:
            if self.after_id:
                 self.root.after_cancel(self.after_id)
                 self.after_id = None
            return

        # 遍历所有活动的摄像头进行更新
        # 使用 list(self.cameras.keys()) 避免在迭代时修改字典
        for index in list(self.cameras.keys()):
            camera_state = self.cameras.get(index)
            # 检查摄像头是否仍然有效且正在捕获
            if camera_state and camera_state['cap'] and camera_state['cap'].isOpened():
                 frame_to_display = None
                 # 从共享变量获取最新帧，使用锁保护
                 with camera_state['lock']:
                     if camera_state['latest_frame'] is not None:
                         frame_to_display = camera_state['latest_frame']
                         camera_state['latest_frame'] = None # 清空，表示已获取

                 if frame_to_display is not None:
                     try:
                         # 将OpenCV图像转换为PIL图像
                         img = Image.fromarray(cv2.cvtColor(frame_to_display, cv2.COLOR_BGR2RGB))

                         # 调整图像大小以适应其显示区域
                         cell_width, cell_height = camera_state['display_size']
                         if cell_width > 1 and cell_height > 1:
                              img_width, img_height = img.size
                              if img_width > 0 and img_height > 0:
                                  ratio = min(cell_width / img_width, cell_height / img_height)
                                  new_size = (int(img_width * ratio), int(img_height * ratio))
                                  img = img.resize(new_size, Image.BILINEAR)

                         # 转换为Tkinter PhotoImage
                         # 必须更新存储在状态字典里的photo引用
                         camera_state['photo'] = ImageTk.PhotoImage(image=img)

                         # 确保image item ID有效并更新
                         if camera_state['image_item_id'] is not None:
                             self.video_canvas.itemconfig(camera_state['image_item_id'], image=camera_state['photo'])

                     except tk.TclError as e:
                         print(f"更新摄像头 {index} 画布图像时发生Tcl错误: {e}")
                         # 如果发生错误，可能窗口正在关闭或摄像头有问题，停止捕获该摄像头
                         self.stop_camera(index) # 停止特定的摄像头
                     except Exception as e:
                          print(f"更新摄像头 {index} 画布图像时发生其他错误: {e}")
                          # self.stop_camera(index) # 是否停止取决于错误类型
            elif index in self.cameras: # 如果cap无效或未打开，但仍在字典中，说明捕获线程已退出或失败
                 print(f"摄像头 {index} 状态异常，正在清理...")
                 self.stop_camera(index) # 清理该摄像头资源和UI

        # 每30毫秒调度下一次UI更新 (约33 FPS)
        if self.is_capturing and self.cameras: # 只有当仍在捕获且至少有一个摄像头是活动的才继续调度
             self.after_id = self.root.after(30, self.schedule_ui_update)
        else:
             # 如果没有活动的摄像头了，停止调度
             if self.after_id:
                  self.root.after_cancel(self.after_id)
                  self.after_id = None
             if not self.cameras and self.is_capturing: # 所有摄像头都停止了，但is_capturing还是True
                  self.is_capturing = False # 重置状态
                  self.show_message("所有摄像头已关闭")

    # 新增方法：停止单个摄像头的捕获和清理
    def stop_camera(self, index):
        if index in self.cameras:
            camera_state = self.cameras[index]
            
            # 设置捕获标志，通知线程退出 (如果线程逻辑依赖这个标志的话)
            # is_capturing 是全局的，这里不能设置 False
            
            # 释放摄像头资源
            if camera_state['cap'] and camera_state['cap'].isOpened():
                camera_state['cap'].release()
                print(f"摄像头 {index} 已释放")
            camera_state['cap'] = None
            
            # 确保线程结束
            if camera_state['thread'] and camera_state['thread'].is_alive():
                 print(f"正在等待摄像头 {index} 捕获线程结束...")
                 camera_state['thread'].join(timeout=1.0)
                 if camera_state['thread'].is_alive():
                      print(f"警告: 摄像头 {index} 捕获线程未能及时结束。")
                      
            # 清空该摄像头的Canvas图像
            if camera_state['image_item_id'] is not None:
                try:
                    self.video_canvas.delete(camera_state['image_item_id'])
                except tk.TclError:
                    pass # 忽略可能已删除的错误

            # 从字典中移除该摄像头状态
            del self.cameras[index]
            print(f"摄像头 {index} 状态已清理。")
            
            # 如果没有活动摄像头了，更新状态信息并停止UI定时器
            if not self.cameras:
                 self.is_capturing = False
                 if self.after_id:
                      self.root.after_cancel(self.after_id)
                      self.after_id = None
                 self.show_message("所有摄像头已关闭")

    # 修改：停止所有摄像头的捕获和清理
    def stop_all_captures(self):
        self.is_capturing = False # 设置全局标志，通知所有线程退出
        
        # 取消定时UI更新
        if self.after_id:
             self.root.after_cancel(self.after_id)
             self.after_id = None
             
        # 遍历所有活动的摄像头并停止它们
        # 使用 list() 创建键的副本，以便在迭代时可以删除字典项
        for index in list(self.cameras.keys()):
             self.stop_camera(index)
             
        # 清空画布（如果还有残留的元素）
        self.clear_canvas()
        # 确保提示信息显示
        if not self.cameras:
             self.show_message("所有摄像头已关闭")


    def show_error(self, message):
        # 检查窗口是否还存在
        if not self.root.winfo_exists():
             return
             
        # 清空画布上的所有图像项 (避免和错误信息重叠)
        # 只清理图像项，保留可能的其他元素，但这可能需要更精细的canvas item管理
        # 简单起见，这里还是清空所有，或者只删除图像item
        # For now, let's clear all graphical elements
        self.clear_canvas() # 假设 clear_canvas 删除所有东西
        
        self.video_canvas.create_text(
            self.video_canvas.winfo_width() // 2,
            self.video_canvas.winfo_height() // 2,
            text=message,
            fill="red",
            font=("Arial", 16),
            anchor=tk.CENTER
        )

    def show_message(self, message):
         # 检查窗口是否还存在
         if not self.root.winfo_exists():
              return
              
         # 清空画布上的所有图像项
         self.clear_canvas()

         self.video_canvas.create_text(
             self.video_canvas.winfo_width() // 2,
             self.video_canvas.winfo_height() // 2,
             text=message,
             fill="white",
             font=("Arial", 16),
             anchor=tk.CENTER
         )


    def clear_canvas(self):
         self.video_canvas.delete("all")

    def on_closing(self):
        self.stop_all_captures()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = CameraApp(root)
    root.mainloop()
