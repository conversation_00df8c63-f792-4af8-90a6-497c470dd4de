import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from PIL import Image, ImageTk
import os
import time

class GroundDistanceCalculator:
    """
    地面距离计算器类
    用于计算图像中点到参考边的实际距离，以及两点之间的实际距离
    基于透视变换原理，将图像坐标映射到实际地面坐标
    """
    def __init__(self):
        # 初始化相机参数(需通过标定获取实际值)
        # 相机矩阵包含焦距和光学中心信息
        self.camera_matrix = np.array([[1000, 0, 640], [0, 1000, 360], [0, 0, 1]])
        # 畸变系数，用于校正镜头畸变
        self.dist_coeffs = np.array([-0.3, 0.1, 0, 0])

        # 地面矩形实际尺寸 - 设置默认值，允许用户修改
        self.ground_width = 7.0 # 水平方向长度（左右方向）
        self.ground_height = 4.0 # 垂直方向宽度（上下方向）
        self.ground_points = None # 将在使用前根据尺寸生成
        # self.update_ground_points() # 在__init__中调用可能过早，移到UI创建后

        # 图像控制点和地面控制点
        self.img_points = None  # 将在用户标记后设置
        self.original_img = None # 添加原始图像属性
        self.undistorted_img = None # 添加处理后图像属性
        # 地面控制点定义了实际坐标系统
        # 确保地面控制点正确对应实际尺寸：左右7米，上下4米
        self.ground_points = np.array([
            [0, 0],                              # 左上角 - 原点
            [self.ground_width, 0],              # 右上角 - 水平方向7米
            [self.ground_width, self.ground_height],  # 右下角 - 水平7米，垂直4米
            [0, self.ground_height]              # 左下角 - 垂直方向4米
        ], dtype=np.float32)

        # 透视变换矩阵 - 用于将图像坐标转换为地面实际坐标
        self.H = None

    def load_image(self, img_path):
        """
        加载图像（暂时去掉畸变矫正）
        参数:
            img_path: 图像文件路径
        返回:
            原始图像（不进行畸变矫正）
        """
        # 读取原始图像
        self.original_img = cv2.imread(img_path)
        if self.original_img is None:
            raise ValueError("无法加载图像")

        # 暂时跳过畸变矫正步骤，直接使用原始图像
        self.undistorted_img = self.original_img.copy()

        return self.undistorted_img

    def set_image_points(self, points, scale_ratio=1.0):
        """
        设置图像控制点(四个角点)
        参数:
            points: 界面上标记的四个点坐标
            scale_ratio: 图像缩放比例，用于将界面坐标转换回原始图像坐标
        """
        if len(points) != 4:
            raise ValueError("需要4个控制点")

        # 将画布坐标转换为原始图像坐标
        # 因为界面上显示的图像可能经过了缩放
        original_points = []
        for x, y in points:
            original_x = int(x / scale_ratio)
            original_y = int(y / scale_ratio)
            original_points.append((original_x, original_y))

        self.img_points = np.array(original_points, dtype=np.float32)

        # 计算透视变换矩阵
        # 这个矩阵将图像坐标映射到地面实际坐标
        self.H = cv2.getPerspectiveTransform(
            self.img_points,  # 源坐标（图像中的四个点）
            self.ground_points  # 目标坐标（地面实际坐标系中的四个点）
        )

    def calculate_distance(self, x_pixel, y_pixel):
        """
        计算点到各边的距离
        参数:
            x_pixel, y_pixel: 图像中点的坐标
        返回:
            包含到左边和底边距离的字典
        """
        if self.H is None:
            raise ValueError("请先设置图像控制点")

        # 将点坐标转换为适合透视变换的格式
        point = np.array([[[x_pixel, y_pixel]]], dtype=np.float32)
        # 应用透视变换，将图像坐标转换为地面实际坐标
        ground_point = cv2.perspectiveTransform(point, self.H)
        x, y = ground_point[0][0]

        # 计算点到左边和底边的实际距离
        return {
            "left": x,  # 水平方向距离左边的距离
            "bottom": self.ground_height - y  # 垂直方向距离底边的距离
        }

    def visualize(self, point=None):
        """
        可视化结果
        参数:
            point: 要测量的点坐标
        返回:
            可视化后的图像
        """
        if self.undistorted_img is None:
            raise ValueError("请先加载图像")

        # 复制图像以便绘制
        img = self.undistorted_img.copy()

        # 绘制控制点
        if self.img_points is not None:
            for pt in self.img_points:
                cv2.circle(img, tuple(pt.astype(int)), 10, (0,255,0), -1)

        # 绘制指定点及其距离线
        if point is not None:
            x, y = point
            # 绘制测量点
            cv2.circle(img, (int(x), int(y)), 8, (0,0,255), -1)

            # 计算并显示距离
            dist = self.calculate_distance(x, y)
            cv2.putText(img, f"左: {dist['left']:.2f}m", (10,30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
            cv2.putText(img, f"下: {dist['bottom']:.2f}m", (10,70),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)

            # 绘制距离线 - 确保与计算方法一致
            cv2.line(img, (int(x), int(y)), (0, int(y)), (255, 0, 0), 2)  # 左边线
            cv2.line(img, (int(x), int(y)), (int(x), img.shape[0]), (255, 0, 0), 2)  # 下边线

        return img

    def update_ground_points(self):
        """根据当前的 ground_width 和 ground_height 更新地面控制点"""
        self.ground_points = np.array([
            [0, 0],
            [self.ground_width, 0],
            [self.ground_width, self.ground_height],
            [0, self.ground_height]
        ], dtype=np.float32)


class GroundDistanceApp:
    """
    地面距离测量应用类
    提供图形界面，允许用户加载图像、标记控制点、测量距离
    """
    def __init__(self, root):
        """
        初始化应用
        参数:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("地面距离测量系统")
        # 增加窗口宽度以容纳右侧并排元素
        self.root.geometry("800x700")

        # 创建计算器实例
        self.calculator = GroundDistanceCalculator()

        # 初始化测量相关属性
        self.measure_marker = None  # 测量点的界面标记
        self.measure_point = None   # 测量点的坐标
        self.measure_button = None  # 测量按钮

        # 初始化两点测距相关属性
        self.two_points_mode = False  # 两点测距模式标志
        self.two_points = []          # 两点测距的点坐标
        self.two_points_markers = []  # 两点测距的界面标记

        # 初始化地面尺寸输入变量
        self.input_width = tk.DoubleVar(value=self.calculator.ground_width)
        self.input_height = tk.DoubleVar(value=self.calculator.ground_height)

        # ---> 添加分辨率选择变量 <---
        self.resolution_var = tk.StringVar(value="720p") # 默认720p

        # 创建界面布局
        self.create_widgets()

        # 初始化地面点（使用默认尺寸）
        self.calculator.update_ground_points()

        # --- 初始化UI状态相关的变量 ---
        self.measure_marker = None
        self.measure_point = None
        self.two_points_mode = False
        self.two_points = []
        self.two_points_markers = []
        self.click_points = []
        self.point_markers = []
        self.pil_img = None # Track loaded PIL image
        self.display_img = None # Track displayed PhotoImage
        self.scale_ratio = 1.0 # Initialize scale ratio

        # Ensure the distortion directory exists
        self.distortion_dir = os.path.join(os.path.dirname(__file__), "distortion")
        os.makedirs(self.distortion_dir, exist_ok=True)

    def create_widgets(self):
        """创建界面组件"""
        # 创建顶部容器框架
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill=tk.X, padx=10, pady=5, side=tk.TOP)

        # --- 左侧控制面板 ---
        # 减少左侧面板的填充，使其更紧凑
        control_frame_left = ttk.LabelFrame(top_frame, text="控制")
        control_frame_left.pack(side=tk.LEFT, padx=(0, 5), pady=5, anchor=tk.NW, fill=tk.Y) # 左对齐，垂直填充

        # --- 图像加载 ---
        load_frame = ttk.Frame(control_frame_left)
        load_frame.pack(pady=5, fill=tk.X)
        ttk.Button(load_frame, text="选择图像文件", command=self.load_image_from_file).pack(side=tk.LEFT, padx=5)
        self.save_image_button = ttk.Button(load_frame, text="保存当前图像", command=self.save_current_image, state=tk.DISABLED)
        self.save_image_button.pack(side=tk.LEFT, padx=5)

        # --- 数据导入导出 ---
        data_io_frame = ttk.Frame(control_frame_left)
        data_io_frame.pack(pady=5, fill=tk.X)
        self.import_button = ttk.Button(data_io_frame, text="导入数据", command=self.import_data, state=tk.DISABLED)
        self.import_button.pack(side=tk.LEFT, padx=5)
        self.export_button = ttk.Button(data_io_frame, text="导出数据", command=self.export_data, state=tk.DISABLED)
        self.export_button.pack(side=tk.LEFT, padx=5)

        # --- 摄像头捕获 ---
        camera_frame = ttk.Frame(control_frame_left)
        camera_frame.pack(pady=5, fill=tk.X)
        ttk.Label(camera_frame, text="选择摄像头:").pack(side=tk.LEFT, padx=5)
        self.camera_index = tk.IntVar(value=0)
        for i in range(3): # 创建 0, 1, 2 的单选按钮
            ttk.Radiobutton(camera_frame, text=str(i), variable=self.camera_index, value=i).pack(side=tk.LEFT)
        ttk.Button(camera_frame, text="捕获图像", command=self.capture_from_camera).pack(side=tk.RIGHT, padx=5)

        # --- 分辨率选择 ---
        resolution_frame = ttk.Frame(control_frame_left)
        resolution_frame.pack(pady=5, fill=tk.X)
        ttk.Label(resolution_frame, text="分辨率:").pack(side=tk.LEFT, padx=5)
        resolutions = {"1080p": "1080p", "720p": "720p", "480p": "480p"}
        for text, value in resolutions.items():
            ttk.Radiobutton(resolution_frame, text=text, variable=self.resolution_var, value=value).pack(side=tk.LEFT, padx=2)

        # --- 测量控制 ---
        measure_control_frame = ttk.Frame(control_frame_left)
        measure_control_frame.pack(pady=10, fill=tk.X)
        self.measure_button = ttk.Button(measure_control_frame, text="开始测量",
                                      command=self.start_measurement, state=tk.DISABLED)
        self.measure_button.pack(side=tk.LEFT, padx=5)
        self.two_points_button = ttk.Button(measure_control_frame, text="两点测距",
                                         command=self.toggle_two_points_mode, state=tk.DISABLED)
        self.two_points_button.pack(side=tk.LEFT, padx=5)

        # --- (修改) 右侧主面板框架 ---
        right_panel_frame = ttk.Frame(top_frame)
        right_panel_frame.pack(side=tk.LEFT, padx=(5, 0), pady=5, fill=tk.BOTH, expand=True) # 填充并扩展

        # --- (新增) 右侧左边的子框架 (矩阵和尺寸) ---
        matrix_and_dims_frame = ttk.Frame(right_panel_frame)
        matrix_and_dims_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5)) # 左侧，垂直填充

        # --- 透视变换矩阵显示区域 (移入 matrix_and_dims_frame) ---
        self.matrix_frame = ttk.LabelFrame(matrix_and_dims_frame, text="透视变换矩阵")
        self.matrix_frame.pack(side=tk.TOP, fill=tk.X, anchor=tk.NW) # 填充X，靠上靠左
        self.matrix_text = tk.Text(self.matrix_frame, height=5, width=35, font=('Courier', 9))
        self.matrix_text.pack(padx=5, pady=5, fill=tk.X)
        self.matrix_text.config(state=tk.DISABLED)

        # --- 地面尺寸设置 (移入 matrix_and_dims_frame) ---
        dimension_frame = ttk.LabelFrame(matrix_and_dims_frame, text="参考矩形实际尺寸(米)", padding=5)
        dimension_frame.pack(side=tk.TOP, pady=(10, 0), fill=tk.X, anchor=tk.NW)
        ttk.Label(dimension_frame, text="宽度:").pack(side=tk.LEFT, padx=(5, 2))
        ttk.Entry(dimension_frame, textvariable=self.input_width, width=6).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(dimension_frame, text="高度:").pack(side=tk.LEFT, padx=(5, 2))
        ttk.Entry(dimension_frame, textvariable=self.input_height, width=6).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(dimension_frame, text="应用", command=self.update_ground_dimensions).pack(side=tk.LEFT, padx=(5, 5))

        # --- (新增) 右侧右边的子框架 (状态和结果) ---
        status_and_results_frame = ttk.Frame(right_panel_frame)
        status_and_results_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) # 左侧(相对右面板)，填充并扩展

        # --- 状态显示 (移入 status_and_results_frame) ---
        status_frame = ttk.LabelFrame(status_and_results_frame, text="状态", padding=5)
        status_frame.pack(side=tk.TOP, pady=(0, 10), fill=tk.X, anchor=tk.NW) # 减少pady
        self.status_var = tk.StringVar(value="就绪")
        # 调整 wraplength 以适应新的宽度
        status_label = ttk.Label(status_frame, textvariable=self.status_var, wraplength=300)
        status_label.pack(fill=tk.X)

        # --- 结果展示区域 (移入 status_and_results_frame) ---
        self.result_frame = ttk.LabelFrame(status_and_results_frame, text="测量结果", padding=5)
        self.result_frame.pack(side=tk.TOP, pady=0, fill=tk.BOTH, expand=True, anchor=tk.NW) # 填充并扩展
        self.result_var = tk.StringVar()
        result_label = ttk.Label(self.result_frame, textvariable=self.result_var, anchor=tk.NW) # 左上对齐
        result_label.pack(fill=tk.BOTH, expand=True)

        # 图像显示区域 - 保持不变
        self.image_frame = ttk.LabelFrame(self.root, text="图像显示")
        self.image_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5, side=tk.TOP)

        # 创建画布用于显示图像和交互
        self.canvas = tk.Canvas(self.image_frame)
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定点击事件处理
        self.canvas.bind("<Button-1>", self.on_canvas_click)

        # 存储点击点和标记
        self.click_points = []  # 控制点坐标
        self.point_markers = [] # 控制点界面标记

    def toggle_two_points_mode(self):
        """切换两点测距模式"""
        self.two_points_mode = not self.two_points_mode

        # 清除之前的两点测距点和标记
        self.two_points = []
        for marker in self.two_points_markers:
            self.canvas.delete(marker)
        self.two_points_markers = []

        # 更新按钮文本和状态提示
        if self.two_points_mode:
            self.two_points_button.config(text="取消两点测距")
            self.status_var.set("两点测距模式：请在矩形内点击第一个点")
        else:
            self.two_points_button.config(text="两点测距")
            self.status_var.set("已退出两点测距模式")

    def handle_two_points_click(self, event):
        """
        处理两点测距模式下的点击
        记录点坐标、绘制标记和连线，计算距离
        """
        canvas_x, canvas_y = event.x, event.y

        # 如果已经有两个点，清除之前的点和标记
        if len(self.two_points) == 2:
            self.two_points = []
            for marker in self.two_points_markers:
                self.canvas.delete(marker)
            self.two_points_markers = []

        # 添加新点
        self.two_points.append((canvas_x, canvas_y))

        # 绘制点标记
        marker = self.canvas.create_oval(
            event.x-5, event.y-5, event.x+5, event.y+5,
            fill='yellow', outline='black', tags="two_point_marker"
        )
        self.two_points_markers.append(marker)

        # 显示点序号
        text_marker = self.canvas.create_text(
            event.x, event.y-15,
            text=f"P{len(self.two_points)}",
            fill='yellow', tags="two_point_marker"
        )
        self.two_points_markers.append(text_marker)

        # 如果有两个点，绘制连线并计算距离
        if len(self.two_points) == 2:
            # 绘制连线
            line_marker = self.canvas.create_line(
                self.two_points[0][0], self.two_points[0][1],
                self.two_points[1][0], self.two_points[1][1],
                fill='yellow', width=2, tags="two_point_marker"
            )
            self.two_points_markers.append(line_marker)

            # 计算两点之间的距离
            self.calculate_two_points_distance()

    def calculate_two_points_distance(self):
        """
        计算两点之间的实际距离
        将界面坐标转换为原始图像坐标，再转换为地面实际坐标，计算欧氏距离
        """
        if len(self.two_points) != 2:
            return

        # 转换为原始图像坐标（去除缩放影响）
        original_points = []
        for x, y in self.two_points:
            original_x = int(x / self.scale_ratio)
            original_y = int(y / self.scale_ratio)
            original_points.append((original_x, original_y))

        # 计算地面实际坐标
        ground_points = []
        for x, y in original_points:
            # 将点坐标转换为适合透视变换的格式
            point = np.array([[[x, y]]], dtype=np.float32)
            # 应用透视变换，将图像坐标转换为地面实际坐标
            ground_point = cv2.perspectiveTransform(point, self.calculator.H)
            ground_points.append(ground_point[0][0])

        # 计算欧氏距离
        x1, y1 = ground_points[0]
        x2, y2 = ground_points[1]
        distance = np.sqrt((x2-x1)**2 + (y2-y1)**2)

        # 输出调试信息
        print(f"地面坐标点1: ({x1:.2f}, {y1:.2f})")
        print(f"地面坐标点2: ({x2:.2f}, {y2:.2f})")
        print(f"计算距离: {distance:.2f}m")

        # 显示结果
        result_text = f"两点之间的距离: {distance:.2f}m"
        self.result_var.set(result_text)

        # 在连线中点显示距离
        mid_x = (self.two_points[0][0] + self.two_points[1][0]) / 2
        mid_y = (self.two_points[0][1] + self.two_points[1][1]) / 2
        text_marker = self.canvas.create_text(
            mid_x, mid_y-10,
            text=f"{distance:.2f}m",
            fill='white', font=('Arial', 10, 'bold'), tags="two_point_marker"
        )
        self.two_points_markers.append(text_marker)

        # 更新状态
        self.status_var.set("两点测距完成")

    def draw_rectangle(self):
        """
        绘制矩形边框
        连接四个控制点形成闭合矩形
        """
        if len(self.click_points) < 4: # Add check
             return

        # 绘制四条边，闭合矩形
        points = self.click_points + [self.click_points[0]]  # 添加第一个点以闭合矩形
        # Create lines and store their IDs if needed for later deletion
        line_markers = []
        for i in range(4):
            line_id = self.canvas.create_line(
                points[i][0], points[i][1],
                points[i+1][0], points[i+1][1],
                fill='green', width=2, tags="shape" # Add tag for easy deletion
            )
            line_markers.append(line_id)
        # Optionally store line_markers if you need to manage them specifically

        # 如果有测量点，绘制测量点 (This part seems misplaced, redraw_points handles measure point)
        # if self.measure_point:
        #     x, y = self.measure_point
        #     self.canvas.create_oval(
        #         x-5, y-5, x+5, y+5,
        #         fill='blue', outline='white'
        #     )

    def redraw_points(self):
        """
        重新绘制控制点、测量点和矩形/连线
        在更新图像或导入数据后恢复所有标记
        """
        # --- Clear existing markers before redrawing ---
        self.clear_markers(clear_control_points=False, clear_measure_point=False, clear_two_points=False, clear_shapes=True) # Clear only shapes initially

        # --- Draw Control Points and Numbers ---
        for i, point in enumerate(self.click_points):
            x, y = point
            # Create new marker - Remove the incorrect winfo_exists check
            marker_oval = self.canvas.create_oval(
               x-5, y-5, x+5, y+5,
               fill='red', outline='white', tags="control_point_marker"
            )
            marker_text = self.canvas.create_text(
               x, y-15,
               text=str(i+1),
               fill='white', tags="control_point_marker"
            )
            # Store markers - Assuming self.point_markers was cleared before calling redraw_points
            # or manage the list appropriately if not always cleared.
            # For simplicity assuming it's cleared by clear_markers before redraw_points is called.
            # If redraw_points might be called without clearing, need to manage self.point_markers list here.
            # Let's stick to the assumption it's cleared for now, and just extend.
            # If redraw needs to be smarter, we'll adjust later.
            # self.point_markers.extend([marker_oval, marker_text]) # This might duplicate if not cleared

            # Safer approach: Clear and rebuild the list here if redraw_points isn't guaranteed a clean slate
            if i == 0:
                 self.point_markers = [] # Clear list only on the first point redraw
            self.point_markers.extend([marker_oval, marker_text])


        # --- Draw Rectangle Border ---
        if len(self.click_points) == 4:
             self.draw_rectangle() # Calls the function to draw lines with "shape" tag

        # --- Draw Measure Point ---
        if self.measure_point:
            x, y = self.measure_point
            # Create or move the measure marker
            if self.measure_marker and self.canvas.winfo_exists(self.measure_marker):
                self.canvas.coords(self.measure_marker, x-5, y-5, x+5, y+5)
            else:
                self.measure_marker = self.canvas.create_oval(
                    x-5, y-5, x+5, y+5,
                    fill='blue', outline='white', tags="measure_marker"
                )

        # --- Redraw Two-Point Measure Markers and Line ---
        if self.two_points_mode or len(self.two_points) > 0: # Also redraw if mode is active but points cleared
            # Clear previous two-point markers specifically if needed
            # self.canvas.delete("two_point_marker") # Example tag
            # self.two_points_markers = [] # Reset list

            for i, point in enumerate(self.two_points):
                x, y = point
                marker_oval = self.canvas.create_oval(
                    x-5, y-5, x+5, y+5,
                    fill='yellow', outline='black', tags="two_point_marker"
                )
                marker_text = self.canvas.create_text(
                    x, y-15,
                    text=f"P{i+1}",
                    fill='yellow', tags="two_point_marker"
                )
                self.two_points_markers.extend([marker_oval, marker_text]) # Add new markers


            # If there are two points, draw the connecting line and distance text
            if len(self.two_points) == 2:
                line_marker = self.canvas.create_line(
                    self.two_points[0][0], self.two_points[0][1],
                    self.two_points[1][0], self.two_points[1][1],
                    fill='yellow', width=2, tags="two_point_marker"
                )
                self.two_points_markers.append(line_marker)

                # Recalculate and display distance if H exists
                if self.calculator.H is not None:
                    # Need to recalculate distance based on current points and H
                    distance = self.get_two_points_distance_value() # New helper needed
                    if distance is not None:
                         mid_x = (self.two_points[0][0] + self.two_points[1][0]) / 2
                         mid_y = (self.two_points[0][1] + self.two_points[1][1]) / 2
                         text_marker = self.canvas.create_text(
                            mid_x, mid_y-10,
                            text=f"{distance:.2f}m",
                            fill='white', font=('Arial', 10, 'bold'), tags="two_point_marker"
                         )
                         self.two_points_markers.append(text_marker)

    def get_two_points_distance_value(self):
         """Helper to calculate and return the distance value between two points."""
         if len(self.two_points) != 2 or self.calculator.H is None or self.scale_ratio == 0:
             return None

         original_points = []
         for x, y in self.two_points:
             original_x = int(x / self.scale_ratio)
             original_y = int(y / self.scale_ratio)
             original_points.append((original_x, original_y))

         ground_points = []
         for x, y in original_points:
             point = np.array([[[x, y]]], dtype=np.float32)
             ground_point = cv2.perspectiveTransform(point, self.calculator.H)
             ground_points.append(ground_point[0][0])

         x1, y1 = ground_points[0]
         x2, y2 = ground_points[1]
         distance = np.sqrt((x2-x1)**2 + (y2-y1)**2)
         return distance

    def on_canvas_click(self, event):
        """
        处理画布点击事件
        根据当前状态执行不同操作：标记控制点、选择测量点或两点测距
        """
        if not hasattr(self, 'pil_img'):
            return  # 如果没有加载图像，不处理点击

        # 如果在两点测距模式下且已设置控制点
        if self.two_points_mode and len(self.click_points) == 4:
            self.handle_two_points_click(event)
            return

        # 如果已经收集了4个控制点，则记录测量点
        if len(self.click_points) == 4:
            # 清除之前的测量点
            if self.measure_marker:
                self.canvas.delete(self.measure_marker)

            # 记录测量点坐标
            canvas_x, canvas_y = event.x, event.y
            self.measure_point = (canvas_x, canvas_y)

            # 绘制测量点标记
            self.measure_marker = self.canvas.create_oval(
                event.x-5, event.y-5, event.x+5, event.y+5,
                fill='blue', outline='white', tags="measure_marker"
            )

            # 启用测量按钮
            self.measure_button.config(state=tk.NORMAL)
            self.status_var.set("已选择测量点，请点击'开始测量'按钮")
            return

        # 记录控制点坐标
        canvas_x, canvas_y = event.x, event.y
        self.click_points.append((canvas_x, canvas_y))

        # 在画布上标记控制点
        marker = self.canvas.create_oval(
            event.x-5, event.y-5, event.x+5, event.y+5,
            fill='red', outline='white', tags="control_point_marker"
        )
        text_marker = self.canvas.create_text(
            event.x, event.y-15,
            text=str(len(self.click_points)),
            fill='white', tags="control_point_marker"
        )
        self.point_markers.extend([marker, text_marker])

        # 如果收集了4个控制点，设置控制点并绘制矩形
        if len(self.click_points) == 4:
            self.calculator.set_image_points(self.click_points, self.scale_ratio)
            self.draw_rectangle()
            self.status_var.set("已设置4个控制点，请点击测量点")
            self.two_points_button.config(state=tk.NORMAL)
            self.export_button.config(state=tk.NORMAL) # Enable export after points are set

            # 显示透视变换矩阵
            self.display_transform_matrix()

    def clear_markers(self, clear_control_points=True, clear_measure_point=True, clear_two_points=True, clear_shapes=True):
         """Helper to clear markers from the canvas."""
         if clear_control_points:
             self.canvas.delete("control_point_marker")
             self.point_markers = []
             # self.click_points = [] # DO NOT clear click_points here
         if clear_measure_point:
             self.canvas.delete("measure_marker")
             self.measure_marker = None
         if clear_two_points:
             self.canvas.delete("two_point_marker")
             self.two_points_markers = []
             self.two_points = [] # Also clear data points
         if clear_shapes:
             self.canvas.delete("shape") # Delete rectangle lines, etc.

    def reset_state(self):
        """重置标记点、测量状态和按钮状态"""
        # --- Clear Markers and Data ---
        self.clear_markers(clear_control_points=True, clear_measure_point=True, clear_two_points=True, clear_shapes=True)

        # --- Reset Calculator State ---
        self.calculator.img_points = None # 重置计算器中的点
        self.calculator.H = None # 重置变换矩阵

        # --- Reset UI Elements ---
        self.measure_button.config(state=tk.DISABLED)
        self.two_points_mode = False
        self.two_points_button.config(text="两点测距", state=tk.DISABLED)
        self.export_button.config(state=tk.DISABLED) # Disable export
        self.save_image_button.config(state=tk.DISABLED) # Correctly disable save image button here
        # Keep import enabled if image is loaded, disable otherwise (handled in load/capture)

        # 清除结果和矩阵显示
        self.result_var.set("")
        self.matrix_text.config(state=tk.NORMAL)
        self.matrix_text.delete(1.0, tk.END)
        # Display initial message if needed
        self.matrix_text.insert(tk.END, "加载图像并标记点或导入数据以查看矩阵")
        self.matrix_text.config(state=tk.DISABLED)

        # Update status - be more specific based on context later
        self.status_var.set("状态已重置，请加载图像或捕获")

    def process_and_display_image(self, img_bgr):
        """处理BGR图像并将其显示在画布上"""
        if img_bgr is None:
            self.status_var.set("错误: 无法获取图像")
            return False

        # 将图像设置到计算器中（此时未矫正）
        self.calculator.original_img = img_bgr.copy()
        self.calculator.undistorted_img = img_bgr.copy() # 直接使用原始图像

        # 重置状态
        self.reset_state()

        # 转换为RGB格式
        img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)

        # 计算缩放比例 (保持宽高比，限制最大尺寸)
        max_width, max_height = 800, 600
        h, w = img_rgb.shape[:2]
        if h == 0 or w == 0:
             self.status_var.set("错误: 图像尺寸无效")
             return False

        self.scale_ratio = min(max_width / w, max_height / h, 1.0) # 增加不超过1的限制
        new_size = (int(w * self.scale_ratio), int(h * self.scale_ratio))

        # 调整图像大小
        try:
            # 使用 LANCZOS 进行高质量缩放
            resized_img_pil = Image.fromarray(img_rgb).resize(new_size, Image.LANCZOS)
        except Exception as resize_err:
             self.status_var.set(f"错误: 调整图像大小时出错: {resize_err}")
             return False

        # 转换为PhotoImage以便在Tkinter中显示
        try:
            self.pil_img = resized_img_pil # 保存 PIL 图像引用
            self.display_img = ImageTk.PhotoImage(image=self.pil_img)
        except Exception as tk_err:
            self.status_var.set(f"错误: 转换图像格式时出错: {tk_err}")
            return False

        # 显示图像在画布上
        self.canvas.config(width=new_size[0], height=new_size[1])
        self.canvas.delete("all") # 清除旧内容
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_img)

        # Enable import button after image is successfully displayed
        self.import_button.config(state=tk.NORMAL)
        # Ensure other buttons are correctly disabled initially
        self.measure_button.config(state=tk.DISABLED)
        self.two_points_button.config(state=tk.DISABLED)
        self.export_button.config(state=tk.DISABLED)
        self.save_image_button.config(state=tk.NORMAL) # Enable save image button

        return True # 表示成功

    def capture_from_camera(self):
        """从选定的摄像头捕获一帧图像"""
        cam_idx = self.camera_index.get()
        self.status_var.set(f"尝试从摄像头 {cam_idx} 捕获...")
        self.root.update_idletasks() # 更新UI显示状态

        cap = None
        try:
            # 使用 DirectShow 后端
            cap = cv2.VideoCapture(cam_idx, cv2.CAP_DSHOW)
            if not cap.isOpened():
                raise IOError(f"无法打开摄像头 {cam_idx}")

            # --- 设置请求的分辨率 ---
            selected_res = self.resolution_var.get()
            if selected_res == "1080p":
                desired_width = 1920
                desired_height = 1080
            elif selected_res == "720p":
                desired_width = 1280
                desired_height = 720
            elif selected_res == "480p":
                desired_width = 640
                desired_height = 480
            else:
                 # Fallback or default if selection is somehow invalid (shouldn't happen with radio buttons)
                 desired_width = 1280
                 desired_height = 720
                 print(f"[Warning] 无效的分辨率选项 '{selected_res}', 使用默认 720p.")

            print(f"尝试设置分辨率为: {desired_width}x{desired_height}")
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, desired_width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, desired_height)

            # --- 检查实际设置的分辨率 (可选) ---
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            print(f"摄像头实际分辨率: {actual_width}x{actual_height}")
            # ----------------------------------

            # 尝试读取并丢弃前25帧以确保稳定
            for _ in range(25): 
                ret, frame = cap.read()
                if not ret:
                    raise IOError(f"无法从摄像头 {cam_idx} 读取帧（丢弃阶段）")
                time.sleep(0.01) # 短暂等待，避免CPU占用过高

            # 读取用于处理的帧
            ret, frame = cap.read()
                 
            if not ret or frame is None:
                 raise IOError(f"无法从摄像头 {cam_idx} 读取帧")
                 
            # 处理并显示图像
            if self.process_and_display_image(frame):
                # 获取图像分辨率
                h, w = frame.shape[:2]
                self.status_var.set(f"已从摄像头 {cam_idx} 捕获图像 ({w}x{h})，请标记4个控制点") # Added resolution
            else:
                 # process_and_display_image 内部已设置错误状态
                 pass

        except Exception as e:
            self.status_var.set(f"捕获错误: {str(e)}")
            messagebox.showerror("摄像头错误", f"无法从摄像头 {cam_idx} 捕获图像:\n{str(e)}")
        finally:
            if cap is not None and cap.isOpened():
                cap.release() # 确保释放摄像头

        self.two_points_button.config(state=tk.NORMAL)

        # 显示透视变换矩阵
        self.display_transform_matrix()

    def load_image_from_file(self):
        """加载图像文件并显示在界面上"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件", # 添加标题
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tif *.tiff")] # 支持更多格式
        )
        if not file_path:
            return

        self.status_var.set(f"正在加载图像: {os.path.basename(file_path)}...")
        self.root.update_idletasks()

        try:
            # 加载图像
            img_bgr = self.calculator.load_image(file_path) # calculator.load_image 现在只加载

            # 处理并显示
            if self.process_and_display_image(img_bgr):
                self.status_var.set(f"已加载图像: {os.path.basename(file_path)}，请标记4个控制点")
            else:
                 # process_and_display_image 内部已设置错误状态
                 pass

        except Exception as e:
            self.status_var.set(f"加载错误: {str(e)}")
            messagebox.showerror("加载错误", f"无法加载图像文件:\n{str(e)}")
            self.import_button.config(state=tk.DISABLED) # Disable import on load failure

    def display_transform_matrix(self):
        """
        在界面上显示透视变换矩阵
        格式化矩阵以便于阅读和复制
        """
        if self.calculator.H is None:
            # 如果矩阵不存在，可以显示提示信息或清空
            self.matrix_text.config(state=tk.NORMAL)
            self.matrix_text.delete(1.0, tk.END)
            self.matrix_text.insert(tk.END, "请先标记4个控制点以计算矩阵")
            self.matrix_text.config(state=tk.DISABLED)
            return

        # 启用文本框编辑
        self.matrix_text.config(state=tk.NORMAL)

        # 清除之前的内容
        self.matrix_text.delete(1.0, tk.END)

        # 格式化矩阵为 Python 列表样式
        matrix = self.calculator.H
        matrix_str = "[" # 开始外层列表
        for i, row in enumerate(matrix):
            # 格式化每一行
            # 使用 f-string 控制浮点数格式，例如保留8位小数
            row_str = ", ".join([f"{val:.8f}" for val in row])
            matrix_str += f"\n    [{row_str}]" # 添加内层列表括号和缩进
            if i < len(matrix) - 1:
                matrix_str += "," # 添加行间逗号

        matrix_str += "\n]" # 结束外层列表

        self.matrix_text.insert(tk.END, matrix_str)

        # 禁用文本框编辑（只读模式）
        self.matrix_text.config(state=tk.DISABLED)

    def update_ground_dimensions(self):
        """更新地面矩形的实际尺寸"""
        try:
            new_width = self.input_width.get()
            new_height = self.input_height.get()

            if new_width <= 0 or new_height <= 0:
                raise ValueError("宽度和高度必须为正数")

            # 更新计算器中的尺寸
            self.calculator.ground_width = new_width
            self.calculator.ground_height = new_height

            # 重新计算地面点
            self.calculator.update_ground_points()
            self.log_message(f"参考矩形尺寸已更新为: 宽 {new_width:.2f}m, 高 {new_height:.2f}m")

            # 如果已经设置了图像点，则重新计算变换矩阵H
            if self.calculator.img_points is not None and len(self.calculator.img_points) == 4:
                self.calculator.H = cv2.getPerspectiveTransform(
                    self.calculator.img_points,
                    self.calculator.ground_points
                )
                self.log_message("透视变换矩阵已基于新尺寸重新计算")
                self.display_transform_matrix()
                self.root.update_idletasks() # Ensure GUI refreshes

            # 如果有测量点，可能需要重新计算结果
            if self.measure_point:
                 self.log_message("尺寸已更新，请重新点击'开始测量'以获取新结果")
                 self.measure_button.config(state=tk.NORMAL) # 允许重新测量
                 self.result_var.set("尺寸已更新，请重新测量")

            # 如果有两点测距，也需要重新计算
            if len(self.two_points) == 2:
                 self.log_message("尺寸已更新，两点测距结果已失效")
                 # 清除旧的两点测距标记和结果，但不清除点本身
                 for i in range(len(self.two_points_markers)):
                      if i >= 4: # 保留 P1, P2 标记
                           self.canvas.delete(self.two_points_markers[i])
                 self.two_points_markers = self.two_points_markers[:4]
                 self.result_var.set("尺寸已更新，请重新进行两点测距")

            self.status_var.set("参考矩形尺寸已应用")

        except ValueError as e:
            messagebox.showerror("输入错误", f"请输入有效的数字尺寸:\n{str(e)}")
        except tk.TclError:
             messagebox.showerror("输入错误", "请输入有效的数字尺寸")
        except Exception as e: # Catch any other unexpected errors
             messagebox.showerror("更新尺寸时出错", f"发生意外错误: {str(e)}")
             self.status_var.set(f"更新尺寸时出错: {e}")

    def log_message(self, message):
         """Helper function to log messages (e.g., print to console)."""
         print(f"[LOG] {message}") # Simple console logging for now

    def export_data(self):
         """导出透视变换矩阵H、逆矩阵H_inv、图像控制点img_points、缩放比例和地面尺寸到文件"""
         if self.calculator.H is None or self.calculator.img_points is None:
             messagebox.showwarning("导出错误", "没有可导出的数据。\n请先标记4个控制点或导入有效数据。") # Updated message
             return
         if not hasattr(self, 'scale_ratio') or self.scale_ratio <= 0: # Add check for scale_ratio
             messagebox.showwarning("导出错误", "无法获取有效的图像缩放比例。")
             return

         file_path = filedialog.asksaveasfilename(
             title="导出变换数据",
             initialdir=self.distortion_dir,
             defaultextension=".txt", # Changed from .npz
             filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")] # Changed file types
         )

         if not file_path:
             return # User cancelled

         try:
             # 计算逆矩阵（用于世界坐标到图像坐标的转换）
             H_inv = np.linalg.inv(self.calculator.H)

             # Format data as strings and write to text file
             with open(file_path, 'w') as f:
                 f.write("# --- 透视变换标定数据 ---\n")
                 f.write("\n")
                 f.write("\n")

                 # 导出停车区域的长和宽
                 f.write(f"ptm1_ground_width = {self.calculator.ground_width:.2f}\n")
                 f.write(f"ptm1_ground_height = {self.calculator.ground_height:.2f}\n")
                 f.write("\n")

                 f.write("H (Perspective Transform Matrix):\n")
                 # 使用循环格式化每一行矩阵
                 for i, row in enumerate(self.calculator.H):
                     row_str = ", ".join([f"{val:.8e}" for val in row]) # 使用科学计数法
                     f.write(f"ptm1_row{i+1} = {row_str}\n")
                 f.write("\n")

                 f.write("H_inv (Inverse Transform Matrix):\n")
                 # 导出逆矩阵
                 for i, row in enumerate(H_inv):
                     row_str = ", ".join([f"{val:.8e}" for val in row]) # 使用科学计数法
                     f.write(f"ptm1_inv_row{i+1} = {row_str}\n")
                 f.write("\n")

                 f.write("img_points (Original Image Coordinates):\n")
                 # 扁平化 img_points 并格式化
                 points_flat = self.calculator.img_points.flatten()
                 points_str = ", ".join([f"{val:.1f}" for val in points_flat]) # 保持一位小数
                 f.write(f"ptm1_img_points = {points_str}\n")

             self.status_var.set(f"数据已导出到: {os.path.basename(file_path)}")
             messagebox.showinfo("导出成功", f"数据已成功导出到:\n{file_path}")
         except Exception as e:
             self.status_var.set(f"导出失败: {e}")
             messagebox.showerror("导出失败", f"无法导出数据:\n{str(e)}")

    def import_data(self):
        """从文件导入透视变换矩阵H、图像控制点img_points、缩放比例和地面尺寸 (TXT格式)"""
        if not hasattr(self, 'pil_img') or self.pil_img is None:
             messagebox.showwarning("导入错误", "请先加载或捕获图像，然后再导入数据。")
             return

        file_path = filedialog.askopenfilename(
            title="导入变换数据 (txt)",
            initialdir=self.distortion_dir,
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")] # Ensure it looks for txt
        )

        if not file_path:
            return # User cancelled

        try:
            # --- Read and Parse Text File --- #
            data_dict = {}
            current_section = None
            H_rows = []
            H_inv_rows = []
            img_points_str = ""
            ground_width_str = ""
            ground_height_str = ""

            with open(file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    if line.startswith("ptm1_ground_width"):
                        parts = line.split('=', 1)
                        if len(parts) > 1:
                            ground_width_str = parts[1].strip()
                    elif line.startswith("ptm1_ground_height"):
                        parts = line.split('=', 1)
                        if len(parts) > 1:
                            ground_height_str = parts[1].strip()
                    elif line.startswith("H (Perspective Transform Matrix):"):
                        current_section = "H"
                    elif line.startswith("H_inv (Inverse Transform Matrix):"):
                        current_section = "H_inv"
                    elif line.startswith("img_points (Original Image Coordinates):"):
                        current_section = "img_points"
                    elif current_section:
                        if current_section == "H" and line.startswith("ptm1_row"):
                            # Extract numbers after '=' sign
                            parts = line.split('=', 1)
                            if len(parts) > 1:
                                numbers = [float(n) for n in parts[1].strip().split(', ')]
                                H_rows.append(numbers)
                        elif current_section == "H_inv" and line.startswith("ptm1_inv_row"):
                            parts = line.split('=', 1)
                            if len(parts) > 1:
                                numbers = [float(n) for n in parts[1].strip().split(', ')]
                                H_inv_rows.append(numbers)
                        elif current_section == "img_points" and line.startswith("ptm1_img_points"):
                            parts = line.split('=', 1)
                            if len(parts) > 1:
                                img_points_str = parts[1].strip()

            # --- Extract and Convert Data --- #
            # 解析停车区域长宽
            try:
                if ground_width_str:
                    ground_width_loaded = float(ground_width_str)
                    self.log_message(f"从文件加载停车区域宽度: {ground_width_loaded}")
                else:
                    ground_width_loaded = None
                    self.log_message("文件中未找到停车区域宽度，将使用当前值")
                
                if ground_height_str:
                    ground_height_loaded = float(ground_height_str)
                    self.log_message(f"从文件加载停车区域高度: {ground_height_loaded}")
                else:
                    ground_height_loaded = None
                    self.log_message("文件中未找到停车区域高度，将使用当前值")
            except ValueError as e:
                self.log_message(f"解析停车区域尺寸时出错: {e}，将使用当前值")
                ground_width_loaded = None
                ground_height_loaded = None

            # Parse H matrix
            if len(H_rows) == 3 and all(len(row) == 3 for row in H_rows):
                H_loaded = np.array(H_rows, dtype=np.float32)
            else:
                raise ValueError(f"无法从文件解析 H 矩阵，预期 3x3，实际 {len(H_rows)}x{len(H_rows[0]) if H_rows else 0}")

            # Parse H_inv matrix (optional, as it can be derived)
            if len(H_inv_rows) == 3 and all(len(row) == 3 for row in H_inv_rows):
                H_inv_loaded = np.array(H_inv_rows, dtype=np.float32)
                self.log_message("导入文件中包含逆矩阵数据。")
            else:
                H_inv_loaded = None # Or compute if needed later
                self.log_message("导入文件中未找到完整的逆矩阵数据。")

            # Parse img_points string
            if not img_points_str:
                raise ValueError("未找到 img_points 数据")
            try:
                points_flat_list = [float(n) for n in img_points_str.split(', ')]
                if len(points_flat_list) == 8: # Expect 4 points * 2 coordinates = 8 numbers
                    img_points_loaded = np.array(points_flat_list).reshape((4, 2)).astype(np.float32)
                else:
                    raise ValueError(f"无法从字符串解析 4x2 img_points (找到 {len(points_flat_list)} 个数字，需要 8 个)")
            except Exception as e:
                raise ValueError(f"解析 img_points 字符串时出错: {e}\n原始字符串: \n{img_points_str}")

            # --- Validate Data Shapes & Values --- #
            if H_loaded.shape != (3, 3):
                raise ValueError("导入的矩阵 H 形状不正确 (应为 3x3)。")
            if img_points_loaded.shape != (4, 2):
                raise ValueError("导入的图像点 img_points 形状不正确 (应为 4x2)。")

            # --- Reset current state before applying imported data --- #
            self.clear_markers(clear_control_points=True, clear_measure_point=True, clear_two_points=True, clear_shapes=True)
            self.calculator.H = None
            self.calculator.img_points = None
            self.two_points_mode = False # Reset two-point mode as well
            self.result_var.set("") # Clear result display

            # --- Apply Loaded Data --- #
            # 应用停车区域尺寸（如果有）
            if ground_width_loaded is not None:
                self.calculator.ground_width = ground_width_loaded
                self.input_width.set(ground_width_loaded)
                self.log_message(f"应用停车区域宽度: {ground_width_loaded}")
            
            if ground_height_loaded is not None:
                self.calculator.ground_height = ground_height_loaded
                self.input_height.set(ground_height_loaded)
                self.log_message(f"应用停车区域高度: {ground_height_loaded}")
            
            # 更新地面点
            self.calculator.update_ground_points()

            self.calculator.H = H_loaded
            self.calculator.img_points = img_points_loaded # Store original coordinates

            # --- Calculate Canvas Coordinates (click_points) --- #
            self.click_points = []
            if hasattr(self, 'scale_ratio') and self.scale_ratio > 0:
                for x_orig, y_orig in img_points_loaded:
                    canvas_x = x_orig * self.scale_ratio
                    canvas_y = y_orig * self.scale_ratio
                    self.click_points.append((canvas_x, canvas_y))
            else:
                self.status_var.set("错误: 当前图像缩放比例无效，无法显示导入的点。请确保已加载图像并其比例有效。")
                raise ValueError("当前图像缩放比例无效，无法计算画布坐标。")

            # --- Update UI --- #
            self.display_transform_matrix() # Show the loaded matrix
            self.redraw_points() # Draw the points and rectangle based on new click_points

            # Enable relevant buttons now that data is loaded
            self.measure_button.config(state=tk.NORMAL)
            self.two_points_button.config(state=tk.NORMAL, text="两点测距") # Reset text too
            self.export_button.config(state=tk.NORMAL) # Enable export as data is valid

            self.status_var.set(f"已成功从 {os.path.basename(file_path)} 导入数据")
            messagebox.showinfo("导入成功", "变换数据已成功导入。\n您现在可以进行测量。")

        except FileNotFoundError:
            self.status_var.set(f"导入失败: 文件未找到 {os.path.basename(file_path)}")
            messagebox.showerror("导入失败", f"文件未找到:\n{file_path}")
        except Exception as e:
            # Reset state partially on error to avoid inconsistency
            self.clear_markers(clear_control_points=True)
            self.calculator.H = None
            self.calculator.img_points = None
            self.display_transform_matrix() # Clear matrix display
            self.measure_button.config(state=tk.DISABLED)
            self.two_points_button.config(state=tk.DISABLED)
            self.export_button.config(state=tk.DISABLED)
            self.status_var.set(f"导入失败: {e}")
            messagebox.showerror("导入失败", f"无法导入数据:\n{str(e)}")

    def start_measurement(self):
         """
         开始测量
         计算测量点到参考边的距离并显示结果
         """
         if self.measure_point and self.calculator.H is not None:
             # 转换为原始图像坐标（去除缩放影响）
             if self.scale_ratio > 0:
                 original_x = int(self.measure_point[0] / self.scale_ratio)
                 original_y = int(self.measure_point[1] / self.scale_ratio)
             else:
                 self.status_var.set("错误: 缩放比例无效")
                 return

             try:
                 # 计算距离并显示结果
                 distances = self.calculator.calculate_distance(original_x, original_y)
                 result_text = (
                     f"到左边线: {distances['left']:.2f}m\n"
                     f"到底边: {distances['bottom']:.2f}m"
                 )
                 self.result_var.set(result_text)
                 # 更新状态
                 self.status_var.set("测量完成")
                 # 禁用测量按钮 (或者 keep it enabled to re-measure? Let's disable for now)
                 # self.measure_button.config(state=tk.DISABLED)
             except ValueError as e:
                 self.status_var.set(f"测量错误: {e}")
                 messagebox.showerror("测量错误", str(e))
             except Exception as e:
                 self.status_var.set(f"测量时发生意外错误: {e}")
                 messagebox.showerror("测量错误", f"发生意外错误: {str(e)}")
         else:
             self.status_var.set("错误: 未选择测量点或未设置控制点")

    def save_current_image(self):
         """保存当前加载的原始图像到 distortion 文件夹"""
         if self.calculator.original_img is None:
             messagebox.showwarning("保存错误", "没有加载图像可供保存。")
             return

         file_path = filedialog.asksaveasfilename(
             title="保存当前图像",
             initialdir=self.distortion_dir,
             defaultextension=".png",
             filetypes=[("PNG 文件", "*.png"), ("JPEG 文件", "*.jpg;*.jpeg"), ("BMP 文件", "*.bmp"), ("所有文件", "*.*")]
         )

         if not file_path:
             return # User cancelled

         try:
             success = cv2.imwrite(file_path, self.calculator.original_img)
             if success:
                 self.status_var.set(f"图像已保存到: {os.path.basename(file_path)}")
                 messagebox.showinfo("保存成功", f"图像已成功保存到:\n{file_path}")
             else:
                  raise OSError("cv2.imwrite 返回 false") # Raise error if imwrite fails
         except Exception as e:
             self.status_var.set(f"保存失败: {e}")
             messagebox.showerror("保存失败", f"无法保存图像:\n{str(e)}")

# 主程序入口
if __name__ == "__main__":
    root = tk.Tk()
    app = GroundDistanceApp(root)
    root.mainloop()

