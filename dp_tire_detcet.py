import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import os
import random
import math

class TireDetectorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("轮胎检测器")
        self.root.geometry("1200x800")  # 增加窗口大小以容纳更多内容
        
        # 存储当前图像和处理状态
        self.image_path = ""
        self.original_image = None
        self.result_image = None
        
        # 存储中间处理结果
        self.gray_image = None
        self.edges_image = None
        self.closed_image = None
        self.contour_image = None
        
        # 创建界面
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图像选择区域
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="图像文件:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=70).grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(file_frame, text="选择图像", command=self.select_image).grid(row=0, column=2, padx=5)
        
        # 处理按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 添加检测按钮
        self.detect_button = ttk.Button(button_frame, text="开始检测", command=self.detect_tire)
        self.detect_button.pack(side=tk.LEFT, padx=5)
        
        # 添加保存结果按钮
        self.save_button = ttk.Button(button_frame, text="保存结果", command=self.save_result)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(control_frame, textvariable=self.status_var).pack(anchor=tk.W, pady=5)
        
        # 创建图像显示区域 - 使用网格布局显示多个处理步骤
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 第一行：原始图像和灰度图像
        # 原始图像
        original_frame = ttk.LabelFrame(display_frame, text="1. 原始图像")
        original_frame.grid(row=0, column=0, padx=5, pady=5, sticky=tk.NSEW)
        
        self.original_canvas = tk.Canvas(original_frame, bg="black")
        self.original_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 灰度图像
        gray_frame = ttk.LabelFrame(display_frame, text="2. 灰度图像")
        gray_frame.grid(row=0, column=1, padx=5, pady=5, sticky=tk.NSEW)
        
        self.gray_canvas = tk.Canvas(gray_frame, bg="black")
        self.gray_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 第二行：边缘检测和形态学闭操作
        # 边缘检测
        edges_frame = ttk.LabelFrame(display_frame, text="3. 边缘检测")
        edges_frame.grid(row=1, column=0, padx=5, pady=5, sticky=tk.NSEW)
        
        self.edges_canvas = tk.Canvas(edges_frame, bg="black")
        self.edges_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 形态学闭操作
        closed_frame = ttk.LabelFrame(display_frame, text="4. 形态学闭操作")
        closed_frame.grid(row=1, column=1, padx=5, pady=5, sticky=tk.NSEW)
        
        self.closed_canvas = tk.Canvas(closed_frame, bg="black")
        self.closed_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 第三行：轮廓提取和最终结果
        # 轮廓提取
        contour_frame = ttk.LabelFrame(display_frame, text="5. 轮廓提取")
        contour_frame.grid(row=2, column=0, padx=5, pady=5, sticky=tk.NSEW)
        
        self.contour_canvas = tk.Canvas(contour_frame, bg="black")
        self.contour_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 最终结果
        result_frame = ttk.LabelFrame(display_frame, text="6. 最终结果")
        result_frame.grid(row=2, column=1, padx=5, pady=5, sticky=tk.NSEW)
        
        # 创建结果显示的子框架
        result_display_frame = ttk.Frame(result_frame)
        result_display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 结果图像显示区域
        self.result_canvas = tk.Canvas(result_display_frame, bg="black")
        self.result_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加椭圆参数显示区域
        param_display_frame = ttk.LabelFrame(result_frame, text="椭圆参数")
        param_display_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 创建参数显示的网格布局
        param_grid = ttk.Frame(param_display_frame)
        param_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # 中心点坐标
        ttk.Label(param_grid, text="中心点坐标:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.center_var = tk.StringVar(value="--")
        ttk.Label(param_grid, textvariable=self.center_var, font=("Arial", 10)).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 长短轴
        ttk.Label(param_grid, text="长轴/短轴:", font=("Arial", 10, "bold")).grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.axes_var = tk.StringVar(value="--")
        ttk.Label(param_grid, textvariable=self.axes_var, font=("Arial", 10)).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        
        # 旋转角度
        ttk.Label(param_grid, text="旋转角度:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.angle_var = tk.StringVar(value="--")
        ttk.Label(param_grid, textvariable=self.angle_var, font=("Arial", 10)).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 面积
        ttk.Label(param_grid, text="估计面积:", font=("Arial", 10, "bold")).grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.area_var = tk.StringVar(value="--")
        ttk.Label(param_grid, textvariable=self.area_var, font=("Arial", 10)).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
        
        # 添加结果信息变量
        self.result_info_var = tk.StringVar(value="")
        
        # 配置网格权重，使所有单元格大小相等
        for i in range(3):
            display_frame.rowconfigure(i, weight=1)
        for i in range(2):
            display_frame.columnconfigure(i, weight=1)
    
    def select_image(self):
        """选择待处理的图像文件"""
        image_file = filedialog.askopenfilename(
            title="选择轮胎图像",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp"),
                ("所有文件", "*.*")
            ]
        )
        if image_file:
            self.file_path_var.set(image_file)
            self.image_path = image_file
            self.status_var.set(f"已选择图像: {os.path.basename(image_file)}")
            
            # 加载并显示原始图像
            self.load_image(image_file)
    
    def load_image(self, image_path):
        """加载并显示原始图像"""
        try:
            # 使用OpenCV读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("无法读取图像")
            
            # 存储原始图像
            self.original_image = image
            
            # 转换为RGB用于显示
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 显示在原始图像画布上
            self.display_image(image_rgb, self.original_canvas)
            
            # 清空结果
            self.result_image = None
            self.result_canvas.delete("all")
            self.result_info_var.set("")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    def display_image(self, image, canvas, is_gray=False):
        """在画布上显示图像"""
        # 获取画布尺寸
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()
        
        # 如果画布尚未渲染，使用默认尺寸
        if canvas_width <= 1:
            canvas_width = 300
        if canvas_height <= 1:
            canvas_height = 300
        
        # 调整图像大小以适应画布
        h, w = image.shape[:2] if len(image.shape) >= 2 else (image.shape[0], image.shape[0])
        ratio = min(canvas_width/w, canvas_height/h)
        new_size = (int(w*ratio), int(h*ratio))
        
        # 调整图像大小
        resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_AREA)
        
        # 如果是灰度图像，转换为RGB
        if is_gray:
            if len(resized_image.shape) == 2:
                resized_image = cv2.cvtColor(resized_image, cv2.COLOR_GRAY2RGB)
        else:
            # 确保是RGB格式
            if len(resized_image.shape) == 3 and resized_image.shape[2] == 3:
                resized_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
        
        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        
        # 转换为PhotoImage
        tk_image = ImageTk.PhotoImage(image=pil_image)
        
        # 保存引用，防止垃圾回收
        canvas.image = tk_image
        
        # 清除画布并显示图像
        canvas.delete("all")
        canvas.create_image(canvas_width//2, canvas_height//2, image=tk_image)
    
    def save_result(self):
        """保存检测结果"""
        if self.result_image is None:
            messagebox.showwarning("警告", "请先进行轮胎检测")
            return
        
        try:
            # 选择保存路径
            save_path = filedialog.asksaveasfilename(
                title="保存检测结果",
                defaultextension=".jpg",
                filetypes=[
                    ("JPEG图像", "*.jpg"),
                    ("PNG图像", "*.png"),
                    ("所有文件", "*.*")
                ]
            )
            
            if save_path:
                # 保存图像
                cv2.imwrite(save_path, self.result_image)
                self.status_var.set(f"检测结果已保存至: {os.path.basename(save_path)}")
                messagebox.showinfo("成功", f"检测结果已保存至:\n{save_path}")
        
        except Exception as e:
            messagebox.showerror("错误", f"保存图像时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
    
    # 添加RANSAC椭圆拟合相关函数
    def fit_ellipse_direct(self, points):
        """直接法拟合椭圆，返回椭圆参数"""
        # 至少需要5个点来拟合椭圆
        if len(points) < 5:
            return None
        
        # 将点转换为适合计算的格式
        points = np.array(points, dtype=np.float64)
        
        # 构建设计矩阵
        D = np.zeros((len(points), 6))
        for i, (x, y) in enumerate(points):
            D[i] = [x*x, x*y, y*y, x, y, 1]
        
        # 使用SVD求解线性方程组
        try:
            U, S, Vh = np.linalg.svd(D, full_matrices=False)
            a = Vh[-1]  # 最小奇异值对应的右奇异向量
        except:
            return None
        
        # 提取椭圆参数
        A, B, C, D, E, F = a
        
        # 检查是否为椭圆
        if B*B - 4*A*C >= 0:
            return None  # 不是椭圆
        
        # 计算椭圆中心
        x0 = (2*C*D - B*E) / (B*B - 4*A*C)
        y0 = (2*A*E - B*D) / (B*B - 4*A*C)
        
        # 计算半长轴和半短轴
        num = 2 * (A*E*E + C*D*D - B*D*E + (B*B - 4*A*C)*F)
        den1 = (B*B - 4*A*C) * (np.sqrt((A-C)**2 + B*B) - (A+C))
        den2 = (B*B - 4*A*C) * (-np.sqrt((A-C)**2 + B*B) - (A+C))
        
        # 防止除以零
        if abs(den1) < 1e-8 or abs(den2) < 1e-8:
            return None
        
        a_sq = -num / den1
        b_sq = -num / den2
        
        # 检查半轴是否为正值
        if a_sq <= 0 or b_sq <= 0:
            return None
        
        a = np.sqrt(a_sq)
        b = np.sqrt(b_sq)
        
        # 计算旋转角度
        if abs(B) < 1e-8 and abs(A-C) < 1e-8:
            theta = 0
        else:
            theta = 0.5 * np.arctan2(B, (A-C))
        
        # 转换为度数
        theta_deg = theta * 180 / np.pi
        
        # 确保长轴大于短轴
        if a < b:
            a, b = b, a
            theta_deg += 90
        
        # 标准化角度到0-180度
        theta_deg = theta_deg % 180
        
        return (x0, y0), (2*a, 2*b), theta_deg
    
    def ransac_ellipse_fit(self, points, max_iterations=100, threshold=2.0, min_inliers_ratio=0.6):
        """使用RANSAC算法拟合椭圆"""
        if len(points) < 5:
            print(f"点数不足: {len(points)} < 5")
            return None
        
        points = np.array(points).reshape(-1, 2)
        best_model = None
        best_inliers = []
        max_inliers = 0
        
        # 记录尝试次数和成功次数
        attempts = 0
        successful_fits = 0
        
        # RANSAC迭代
        for iteration in range(max_iterations):
            attempts += 1
            # 随机选择5个点
            if len(points) <= 5:
                sample_indices = range(len(points))
            else:
                sample_indices = random.sample(range(len(points)), 5)
            sample_points = points[sample_indices]
            
            # 使用这5个点拟合椭圆
            model = self.fit_ellipse_direct(sample_points)
            if model is None:
                continue
            
            successful_fits += 1
            
            # 计算所有点到椭圆的距离
            inliers = []
            (x0, y0), (major, minor), angle = model
            
            # 将角度转换为弧度
            angle_rad = angle * np.pi / 180
            
            # 计算旋转矩阵
            cos_theta = np.cos(angle_rad)
            sin_theta = np.sin(angle_rad)
            
            # 半长轴和半短轴
            a = major / 2
            b = minor / 2
            
            for i, (x, y) in enumerate(points):
                # 将点平移到椭圆中心
                x_shifted = x - x0
                y_shifted = y - y0
                
                # 将点旋转到椭圆主轴方向
                x_rotated = x_shifted * cos_theta + y_shifted * sin_theta
                y_rotated = -x_shifted * sin_theta + y_shifted * cos_theta
                
                # 计算点到椭圆的距离
                distance = abs(x_rotated**2 / a**2 + y_rotated**2 / b**2 - 1)
                
                # 如果距离小于阈值，则认为是内点
                if distance < threshold:
                    inliers.append(i)
            
            # 计算内点比例
            inlier_ratio = len(inliers) / len(points)
            
            # 更新最佳模型
            if len(inliers) > max_inliers and inlier_ratio >= min_inliers_ratio:
                max_inliers = len(inliers)
                best_inliers = inliers
                best_model = model
        
        # 输出RANSAC统计信息
        print(f"RANSAC统计: 总尝试={attempts}, 成功拟合={successful_fits}, 最大内点数={max_inliers}, 内点比例={max_inliers/len(points) if max_inliers > 0 else 0:.2f}")
        
        # 如果找到了足够好的模型，使用所有内点重新拟合
        if best_model is not None and len(best_inliers) >= 5:
            inlier_points = points[best_inliers]
            refined_model = self.fit_ellipse_direct(inlier_points)
            if refined_model is not None:
                return refined_model
            else:
                print("使用内点重新拟合失败")
        elif best_model is None:
            print("未找到符合条件的模型")
        elif len(best_inliers) < 5:
            print(f"内点数量不足: {len(best_inliers)} < 5")
        
        return best_model
    
    # 修改检测轮胎方法中的椭圆拟合部分
    def detect_tire(self):
        """检测轮胎"""
        if self.original_image is None:
            messagebox.showwarning("警告", "请先选择一张图像")
            return
        
        try:
            # 更新状态
            self.status_var.set("正在检测轮胎...")
            
            # 复制原始图像
            image = self.original_image.copy()
            
            # 1. 边缘检测及预处理
            # 灰度转换
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            self.gray_image = gray
            self.display_image(gray, self.gray_canvas, is_gray=True)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            self.edges_image = edges
            self.display_image(edges, self.edges_canvas, is_gray=True)
            
            # 形态学闭操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
            self.closed_image = closed
            self.display_image(closed, self.closed_canvas, is_gray=True)
            
            # 2. 轮廓提取与筛选
            contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 检查是否找到轮廓
            if not contours:
                self.status_var.set("未检测到轮廓")
                self.center_var.set("--")
                self.axes_var.set("--")
                self.angle_var.set("--")
                self.area_var.set("--")
                return
            
            # 按面积降序排序，选最大轮廓
            contours = sorted(contours, key=cv2.contourArea, reverse=True)
            largest_contour = contours[0]
            
            # 创建轮廓图像 - 只显示最大轮廓
            contour_image = np.zeros_like(image)
            cv2.drawContours(contour_image, [largest_contour], -1, (0, 255, 0), 2)
            self.contour_image = contour_image
            self.display_image(contour_image, self.contour_canvas)
            
            # 3. 使用RANSAC椭圆拟合替代OpenCV的fitEllipse
            if len(largest_contour) >= 5:  # 至少需要5个点
                # 将轮廓点转换为适合RANSAC的格式
                contour_points = largest_contour.reshape(-1, 2)
                
                # 使用RANSAC拟合椭圆
                ellipse_params = self.ransac_ellipse_fit(contour_points, max_iterations=200, threshold=2.0, min_inliers_ratio=0.5)
                
                if ellipse_params is not None:
                    (cx, cy), (MA, ma), angle = ellipse_params
                    
                    # 4. 绘制结果
                    result_image = image.copy()
                    
                    # 将RANSAC拟合的椭圆参数转换为OpenCV绘制椭圆的格式
                    center = (int(cx), int(cy))
                    axes = (int(MA/2), int(ma/2))  # OpenCV需要半轴长度
                    
                    cv2.ellipse(result_image, center, axes, angle, 0, 360, (0, 255, 0), 2)
                    cv2.circle(result_image, center, 5, (0, 0, 255), -1)
                    
                    # 计算椭圆面积
                    area = np.pi * MA * ma / 4
                    
                    # 更新椭圆参数显示
                    self.center_var.set(f"({int(cx)}, {int(cy)})")
                    self.axes_var.set(f"{int(MA)} / {int(ma)} 像素")
                    self.angle_var.set(f"{int(angle)}°")
                    self.area_var.set(f"{int(area)} 平方像素")
                    
                    # 存储结果图像
                    self.result_image = result_image
                    
                    # 显示在结果画布上
                    self.display_image(result_image, self.result_canvas)
                    
                    # 更新状态
                    self.status_var.set("轮胎检测完成 (RANSAC椭圆拟合)")
                else:
                    # 拟合失败时的处理
                    self.status_var.set("RANSAC椭圆拟合失败")
                    self.center_var.set("--")
                    self.axes_var.set("--")
                    self.angle_var.set("--")
                    self.area_var.set("--")
                    
                    # 创建一个带有失败信息的结果图像
                    result_image = image.copy()
                    # 在图像上添加失败信息
                    cv2.putText(result_image, "RANSAC椭圆拟合失败", (50, 50), 
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                    cv2.putText(result_image, "可能原因:", (50, 100), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    cv2.putText(result_image, "1. 轮廓不符合椭圆形状", (50, 140), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    cv2.putText(result_image, "2. 轮廓点分布不均匀", (50, 180), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    cv2.putText(result_image, "3. 内点比例过低", (50, 220), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
                    # 在轮廓图像上绘制轮廓点
                    contour_with_points = image.copy()
                    # 绘制所有轮廓点
                    for point in contour_points:
                        x, y = point
                        cv2.circle(contour_with_points, (int(x), int(y)), 2, (0, 255, 255), -1)
                    
                    # 存储并显示结果图像
                    self.result_image = result_image
                    self.display_image(result_image, self.result_canvas)
                    
                    # 在控制台输出更多调试信息
                    print(f"RANSAC椭圆拟合失败 - 轮廓点数: {len(contour_points)}")
            else:
                self.status_var.set("轮廓点数不足，无法拟合椭圆")
                self.center_var.set("--")
                self.axes_var.set("--")
                self.angle_var.set("--")
                self.area_var.set("--")
                return
            
        except Exception as e:
            messagebox.showerror("错误", f"检测轮胎时出错: {str(e)}")
            self.status_var.set(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    root = tk.Tk()
    app = TireDetectorApp(root)
    root.mainloop()

# 如果直接运行此脚本
if __name__ == "__main__":
    main()