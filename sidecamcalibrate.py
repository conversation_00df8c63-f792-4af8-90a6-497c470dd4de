import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from PIL import Image, ImageTk
import os
import math

class SideCamCalibrator:
    """
    侧视相机校准工具类
    用于加载图像、旋转图像、选择定位点并计算比例关系
    """
    def __init__(self):
        # 初始化图像相关属性
        self.original_img = None  # 原始图像
        self.display_img = None   # 显示用的图像
        self.rotated_img = None   # 旋转后的图像
        self.current_angle = 0    # 当前旋转角度
        self.img_width = 0        # 当前图像宽度
        self.img_height = 0       # 当前图像高度
        
        # 初始化点和距离相关属性
        self.points = []          # 用户选择的点
        self.actual_distance = 0  # 实际距离(cm)
        self.pixel_distance = 0   # 像素距离
        self.ratio = 0            # 比例关系(cm/像素)
        
    def load_image(self, img_path):
        """
        加载图像
        参数:
            img_path: 图像文件路径
        返回:
            加载的图像
        """
        # 读取原始图像
        self.original_img = cv2.imread(img_path)
        if self.original_img is None:
            raise ValueError("无法加载图像")
            
        # 转换颜色空间从BGR到RGB（PIL使用RGB）
        self.display_img = cv2.cvtColor(self.original_img, cv2.COLOR_BGR2RGB)
        self.rotated_img = self.display_img.copy()
        self.current_angle = 0
        self.points = []
        self.img_height, self.img_width = self.display_img.shape[:2]
        
        return self.display_img
    
    def rotate_image(self, clockwise=True):
        """
        旋转图像90度
        参数:
            clockwise: 是否顺时针旋转
        返回:
            旋转后的图像
        """
        if self.original_img is None:
            raise ValueError("请先加载图像")

        # 确定旋转方向
        if clockwise:
            self.current_angle = (self.current_angle + 90) % 360
        else:
            self.current_angle = (self.current_angle - 90) % 360

        # 从原始图像开始旋转，避免累积误差
        img_to_rotate = cv2.cvtColor(self.original_img, cv2.COLOR_BGR2RGB)

        # 根据累积角度进行旋转
        if self.current_angle == 90:
            self.display_img = cv2.rotate(img_to_rotate, cv2.ROTATE_90_CLOCKWISE)
        elif self.current_angle == 180:
            self.display_img = cv2.rotate(img_to_rotate, cv2.ROTATE_180)
        elif self.current_angle == 270:
            self.display_img = cv2.rotate(img_to_rotate, cv2.ROTATE_90_COUNTERCLOCKWISE)
        else:  # 0度或360度
            self.display_img = img_to_rotate.copy()

        # 更新图像尺寸
        self.img_height, self.img_width = self.display_img.shape[:2]

        # 清除已选择的点
        self.points = []

        return self.display_img
    
    def add_point(self, x, y):
        """
        添加一个点
        参数:
            x, y: 点的坐标
        返回:
            当前已选择的点列表
        """
        if len(self.points) < 2:
            self.points.append((x, y))
        return self.points
    
    def clear_points(self):
        """
        清除所有已选择的点
        """
        self.points = []
    
    def calculate_ratio(self, actual_distance):
        """
        计算实际距离与像素距离的比例
        参数:
            actual_distance: 实际距离(cm)
        返回:
            包含计算结果的字典
        """
        if len(self.points) != 2:
            raise ValueError("需要选择两个点")
            
        self.actual_distance = actual_distance
        
        # 计算两点之间的x方向像素差值
        x1, y1 = self.points[0]
        x2, y2 = self.points[1]
        self.pixel_distance = abs(x2 - x1)
        
        # 计算比例关系(cm/像素)
        if self.pixel_distance > 0:
            self.ratio = self.actual_distance / self.pixel_distance
        else:
            self.ratio = 0
            
        return {
            "points": self.points,
            "pixel_distance": self.pixel_distance,
            "actual_distance": self.actual_distance,
            "ratio": self.ratio
        }
    
    def visualize(self):
        """
        可视化结果
        返回:
            可视化后的图像
        """
        if self.display_img is None:
            raise ValueError("请先加载图像")
            
        # 复制图像以便绘制
        img = self.display_img.copy()
        
        # 绘制已选择的点
        for i, (x, y) in enumerate(self.points):
            # 绘制点
            cv2.circle(img, (int(x), int(y)), 5, (255, 0, 0), -1)
            # 标记点的序号
            cv2.putText(img, str(i+1), (int(x)+10, int(y)), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        
        # 如果有两个点，绘制连线
        if len(self.points) == 2:
            x1, y1 = self.points[0]
            x2, y2 = self.points[1]
            cv2.line(img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
        return img


class SideCamCalibrateApp:
    """
    侧视相机校准应用类
    提供图形界面，允许用户加载图像、旋转图像、选择定位点、计算比例关系
    """
    def __init__(self, root):
        """
        初始化应用
        参数:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("侧视相机校准工具")
        self.root.geometry("1000x700")
        
        # 创建校准器实例
        self.calibrator = SideCamCalibrator()
        
        # 初始化UI状态相关的变量
        self.canvas = None
        self.pil_img = None
        self.tk_img = None
        self.scale_ratio = 1.0
        self.point_markers = []
        self.line_marker = None
        self.select_points_mode = False
        self.actual_distance = tk.DoubleVar(value=0.0)
        self.resolution_label = None # 用于显示分辨率的标签
        self.image_x_offset = 0
        self.image_y_offset = 0
        
        # 创建界面布局
        self.create_widgets()
    
    def create_widgets(self):
        """
        创建界面组件
        """
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10), pady=5)
        
        # 图像加载按钮
        load_frame = ttk.Frame(control_frame)
        load_frame.pack(pady=10, fill=tk.X, padx=5)
        ttk.Button(load_frame, text="打开图像文件", command=self.load_image).pack(fill=tk.X)
        
        # 图像旋转按钮
        rotate_frame = ttk.LabelFrame(control_frame, text="图像旋转")
        rotate_frame.pack(pady=10, fill=tk.X, padx=5)
        ttk.Button(rotate_frame, text="顺时针旋转90°", 
                  command=lambda: self.rotate_image(True)).pack(fill=tk.X, pady=2)
        ttk.Button(rotate_frame, text="逆时针旋转90°", 
                  command=lambda: self.rotate_image(False)).pack(fill=tk.X, pady=2)
        
        # 点选择控制
        points_frame = ttk.LabelFrame(control_frame, text="点选择")
        points_frame.pack(pady=10, fill=tk.X, padx=5)
        self.select_points_button = ttk.Button(points_frame, text="选择定位点", 
                                            command=self.toggle_select_points_mode)
        self.select_points_button.pack(fill=tk.X, pady=2)
        ttk.Button(points_frame, text="清除定位点", 
                  command=self.clear_points).pack(fill=tk.X, pady=2)
        
        # 实际距离输入
        distance_frame = ttk.LabelFrame(control_frame, text="实际距离(cm)")
        distance_frame.pack(pady=10, fill=tk.X, padx=5)
        ttk.Entry(distance_frame, textvariable=self.actual_distance, 
                 width=10).pack(pady=5)
        
        # 计算按钮
        calculate_frame = ttk.Frame(control_frame)
        calculate_frame.pack(pady=10, fill=tk.X, padx=5)
        self.calculate_button = ttk.Button(calculate_frame, text="计算数据", 
                                        command=self.calculate_data)
        self.calculate_button.pack(fill=tk.X)
        
        # 分辨率显示区域
        resolution_frame = ttk.LabelFrame(control_frame, text="图像分辨率")
        resolution_frame.pack(pady=10, fill=tk.X, padx=5)
        self.resolution_label = ttk.Label(resolution_frame, text="")
        self.resolution_label.pack(pady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(control_frame, text="计算结果")
        result_frame.pack(pady=10, fill=tk.X, padx=5, expand=True)
        self.result_text = tk.Text(result_frame, height=10, width=30, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.result_text.config(state=tk.DISABLED)
        
        # 创建右侧图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=5)
        
        # 创建画布用于显示图像
        self.canvas = tk.Canvas(image_frame, bg="lightgray")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 绑定画布点击事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        
        # 禁用相关按钮，直到图像加载
        self.disable_buttons()
    
    def load_image(self):
        """
        加载图像文件
        """
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            # 加载图像
            img = self.calibrator.load_image(file_path)
            
            # 清除画布上的所有内容
            self.canvas.delete("all")
            self.point_markers = []
            self.line_marker = None
            
            # 调整图像大小以适应画布
            self.display_image(img)
            
            # 启用相关按钮
            self.enable_buttons()
            
            # 更新分辨率显示
            self.update_resolution_display()
            
            # 重置结果显示
            self.update_result_text("已加载图像: " + os.path.basename(file_path))
            
        except Exception as e:
            messagebox.showerror("错误", str(e))
    
    def display_image(self, img):
        """
        在画布上显示图像，并调整大小以适应800x600的显示区域
        """
        if img is None:
            return

        # 设置固定的显示区域大小
        display_width = 800
        display_height = 600

        # 计算缩放比例
        img_height, img_width = img.shape[:2]

        # 计算缩放比例以适应800x600的显示区域，保持宽高比
        width_ratio = display_width / img_width
        height_ratio = display_height / img_height
        self.scale_ratio = min(width_ratio, height_ratio)

        # 计算调整后的大小
        new_width = int(img_width * self.scale_ratio)
        new_height = int(img_height * self.scale_ratio)

        # 调整图像大小
        pil_img = Image.fromarray(img)
        pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS)

        # 转换为PhotoImage并显示
        self.pil_img = pil_img
        self.tk_img = ImageTk.PhotoImage(pil_img)

        # 确保画布已更新其几何信息
        self.canvas.update_idletasks()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # 在画布中央显示图像
        self.image_x_offset = (canvas_width - new_width) // 2
        self.image_y_offset = (canvas_height - new_height) // 2
        self.canvas.create_image(self.image_x_offset, self.image_y_offset, anchor=tk.NW, image=self.tk_img)
    
    def rotate_image(self, clockwise=True):
        """
        旋转图像
        """
        try:
            # 旋转图像
            img = self.calibrator.rotate_image(clockwise)
            
            # 清除画布上的所有内容
            self.canvas.delete("all")
            self.point_markers = []
            self.line_marker = None
            
            # 显示旋转后的图像
            self.display_image(img)
            
            # 更新分辨率显示
            self.update_resolution_display()
            
            # 更新结果显示
            self.update_result_text(f"图像已{'顺' if clockwise else '逆'}时针旋转90°")
            
        except Exception as e:
            messagebox.showerror("错误", str(e))
    
    def toggle_select_points_mode(self):
        """
        切换点选择模式
        """
        self.select_points_mode = not self.select_points_mode
        
        if self.select_points_mode:
            self.select_points_button.config(text="取消选择")
            self.update_result_text("请在图像上选择两个定位点")
        else:
            self.select_points_button.config(text="选择定位点")
            self.update_result_text("已取消选择模式")
    
    def on_canvas_click(self, event):
        """
        处理画布点击事件
        """
        if not self.select_points_mode or self.pil_img is None:
            return
        
        # 获取点击位置
        x, y = event.x, event.y
        
        # 转换为图像在画布上的相对坐标
        relative_x = x - self.image_x_offset
        relative_y = y - self.image_y_offset

        # 计算缩放后图像的实际宽度和高度
        img_width_scaled, img_height_scaled = self.pil_img.size
        
        # 确保点击点在缩放后图像的有效范围内
        if not (0 <= relative_x < img_width_scaled and 0 <= relative_y < img_height_scaled):
            self.update_result_text("请点击图像区域内")
            return

        # 转换为原始图像坐标
        orig_x = int(relative_x / self.scale_ratio)
        orig_y = int(relative_y / self.scale_ratio)

        # 确保原始坐标不超过原始图像的实际尺寸
        orig_x = min(max(0, orig_x), self.calibrator.img_width - 1)
        orig_y = min(max(0, orig_y), self.calibrator.img_height - 1)

        print(f"\n--- Canvas Click Debug ---")
        print(f"Event Click: ({x}, {y})")
        print(f"Image Offset: ({self.image_x_offset}, {self.image_y_offset})")
        print(f"Relative Click (on scaled image): ({relative_x}, {relative_y})")
        print(f"Scale Ratio: {self.scale_ratio}")
        print(f"Original Image Dims: ({self.calibrator.img_width}, {self.calibrator.img_height})")
        print(f"Converted Original Coords: ({orig_x}, {orig_y})")
        print(f"--------------------------")
        
        # 添加点
        points = self.calibrator.add_point(orig_x, orig_y)
        
        # 在画布上标记点
        marker = self.canvas.create_oval(x-5, y-5, x+5, y+5, fill="red", outline="")
        self.point_markers.append(marker)
        
        # 添加点的序号标签
        label = self.canvas.create_text(x+10, y, text=str(len(points)), fill="red", font=("Arial", 12, "bold"))
        self.point_markers.append(label)
        
        # 如果已选择两个点，绘制连线
        if len(points) == 2:
            # 获取第一个点的画布坐标
            x1, y1 = points[0]
            canvas_x1 = int(x1 * self.scale_ratio) + self.image_x_offset
            canvas_y1 = int(y1 * self.scale_ratio) + self.image_y_offset

            # 绘制连线
            self.line_marker = self.canvas.create_line(canvas_x1, canvas_y1, x, y, fill="green", width=2)

            # 自动退出选择模式
            self.select_points_mode = False
            self.select_points_button.config(text="选择定位点")
            self.update_result_text("已选择两个点，请输入实际距离并点击计算")
    
    def clear_points(self):
        """
        清除所有已选择的点
        """
        # 清除校准器中的点
        self.calibrator.clear_points()
        
        # 清除画布上的标记
        for marker in self.point_markers:
            self.canvas.delete(marker)
        self.point_markers = []
        
        if self.line_marker:
            self.canvas.delete(self.line_marker)
            self.line_marker = None
        
        # 更新结果显示
        self.update_result_text("已清除所有定位点")
    
    def calculate_data(self):
        """
        计算数据
        """
        try:
            # 获取实际距离
            actual_distance = self.actual_distance.get()
            
            if actual_distance <= 0:
                messagebox.showerror("错误", "请输入大于0的实际距离")
                return
            
            # 计算比例关系
            result = self.calibrator.calculate_ratio(actual_distance)
            
            # 更新结果显示
            result_text = f"计算结果:\n\n"
            result_text += f"点1坐标: ({result['points'][0][0]}, {result['points'][0][1]})\n"
            result_text += f"点2坐标: ({result['points'][1][0]}, {result['points'][1][1]})\n\n"
            result_text += f"X方向像素差值: {result['pixel_distance']:.2f} 像素\n"
            result_text += f"实际距离: {result['actual_distance']:.2f} cm\n\n"
            result_text += f"比例关系: {result['ratio']:.6f} cm/像素\n"
            result_text += f"(1像素 = {result['ratio']:.6f} cm)\n\n"
            result_text += f"当前图像分辨率: {self.calibrator.img_width}x{self.calibrator.img_height}\n"
            result_text += f"当前旋转角度: {self.calibrator.current_angle}°\n"
            result_text += f"显示缩放比例: {self.scale_ratio:.4f}"
            
            self.update_result_text(result_text)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))
    
    def update_result_text(self, text):
        """
        更新结果文本框
        """
        self.result_text.config(state=tk.NORMAL)
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, text)
        self.result_text.config(state=tk.DISABLED)
    
    def update_resolution_display(self):
        """
        更新分辨率显示
        """
        if self.calibrator.display_img is not None:
            width = self.calibrator.img_width
            height = self.calibrator.img_height
            self.resolution_label.config(text=f"{width}x{height}")
        else:
            self.resolution_label.config(text="")
    
    def enable_buttons(self):
        """
        启用相关按钮
        """
        self.select_points_button.config(state=tk.NORMAL)
        self.calculate_button.config(state=tk.NORMAL)
    
    def disable_buttons(self):
        """
        禁用相关按钮
        """
        self.select_points_button.config(state=tk.DISABLED)
        self.calculate_button.config(state=tk.DISABLED)


def main():
    root = tk.Tk()
    app = SideCamCalibrateApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()