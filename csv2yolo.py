import pandas as pd
from PIL import Image
import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, StringVar
import glob

class CSV2YOLOConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("CSV转YOLO格式工具")
        self.root.geometry("700x500")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.source_dir = StringVar()
        self.output_dir = StringVar()
        self.csv_file = StringVar()
        self.csv_files_list = []
        
        # 创建UI
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 源目录选择区域
        source_frame = ttk.LabelFrame(main_frame, text="原始数据目录", padding=10)
        source_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(source_frame, textvariable=self.source_dir, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(source_frame, text="选择目录", command=self.select_source_dir).pack(side=tk.RIGHT, padx=5)
        
        # CSV文件选择区域
        csv_frame = ttk.LabelFrame(main_frame, text="CSV文件", padding=10)
        csv_frame.pack(fill=tk.X, pady=5)
        
        self.csv_listbox = tk.Listbox(csv_frame, height=5)
        self.csv_listbox.pack(fill=tk.X, pady=5)
        self.csv_listbox.bind('<<ListboxSelect>>', self.on_csv_select)
        
        ttk.Label(csv_frame, text="已选择:").pack(side=tk.LEFT, padx=5)
        ttk.Label(csv_frame, textvariable=self.csv_file, width=50).pack(side=tk.LEFT, padx=5)
        
        # 输出目录选择区域
        output_frame = ttk.LabelFrame(main_frame, text="YOLO格式输出目录", padding=10)
        output_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(output_frame, textvariable=self.output_dir, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(output_frame, text="选择目录", command=self.select_output_dir).pack(side=tk.RIGHT, padx=5)
        
        # 转换按钮
        convert_frame = ttk.Frame(main_frame, padding=10)
        convert_frame.pack(fill=tk.X, pady=5)
        
        self.convert_button = ttk.Button(convert_frame, text="开始转换", command=self.start_conversion, state=tk.DISABLED)
        self.convert_button.pack(side=tk.RIGHT, padx=5)
        
        # 状态和进度区域
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding=10)
        status_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 输出区域
        output_log_frame = ttk.LabelFrame(main_frame, text="输出信息", padding=10)
        output_log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.output_text = tk.Text(output_log_frame, height=10, width=70)
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(self.output_text, command=self.output_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.output_text.config(yscrollcommand=scrollbar.set)
    
    def select_source_dir(self):
        """选择源数据目录"""
        directory = filedialog.askdirectory(title="选择原始数据目录")
        if directory:
            self.source_dir.set(directory)
            self.log_message(f"已选择原始数据目录: {directory}")
            self.find_csv_files(directory)
    
    def find_csv_files(self, directory):
        """在目录中查找CSV文件"""
        self.csv_files_list = glob.glob(os.path.join(directory, "*.csv"))
        self.csv_listbox.delete(0, tk.END)
        
        if not self.csv_files_list:
            self.log_message("警告: 在选择的目录中未找到CSV文件")
            return
        
        for csv_file in self.csv_files_list:
            self.csv_listbox.insert(tk.END, os.path.basename(csv_file))
        
        self.log_message(f"在目录中找到 {len(self.csv_files_list)} 个CSV文件")
    
    def on_csv_select(self, event):
        """当用户从列表中选择CSV文件时"""
        selection = self.csv_listbox.curselection()
        if selection:
            index = selection[0]
            csv_path = self.csv_files_list[index]
            self.csv_file.set(os.path.basename(csv_path))
            self.log_message(f"已选择CSV文件: {os.path.basename(csv_path)}")
            self.check_convert_button()
    
    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择YOLO格式输出目录")
        if directory:
            self.output_dir.set(directory)
            self.log_message(f"已选择输出目录: {directory}")
            self.check_convert_button()
    
    def check_convert_button(self):
        """检查是否可以启用转换按钮"""
        if self.source_dir.get() and self.output_dir.get() and self.csv_file.get():
            self.convert_button.config(state=tk.NORMAL)
        else:
            self.convert_button.config(state=tk.DISABLED)
    
    def start_conversion(self):
        """开始转换过程"""
        source_dir = self.source_dir.get()
        output_dir = self.output_dir.get()
        csv_filename = self.csv_file.get()
        csv_path = os.path.join(source_dir, csv_filename)
        
        # 确认转换
        if not messagebox.askyesno("确认", f"是否将 {csv_filename} 转换为YOLO格式?\n输出目录: {output_dir}"):
            return
        
        self.log_message("开始转换...")
        self.status_var.set("转换中...")
        self.progress_var.set(0)
        self.convert_button.config(state=tk.DISABLED)
        
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 读取CSV文件
            df = pd.read_csv(csv_path)
            total_rows = len(df)
            
            # 提取所有类别并生成ID映射（按字母排序）
            labels = sorted(df['label'].unique())
            label_to_id = {label: idx for idx, label in enumerate(labels)}
            
            # 保存类别文件
            with open(os.path.join(output_dir, 'classes.txt'), 'w') as f:
                for label in labels:
                    f.write(f"{label}\n")
            
            self.log_message(f"找到 {len(labels)} 个类别: {', '.join(labels)}")
            
            # 缓存图片尺寸以避免重复读取
            image_size_cache = {}
            processed_count = 0
            
            for index, row in df.iterrows():
                # 解析CSV行数据
                img_name = row['image']
                label = row['label']
                xmin = float(row['xmin'])
                ymin = float(row['ymin'])
                xmax = float(row['xmax'])
                ymax = float(row['ymax'])
                
                # 获取图片尺寸
                if img_name in image_size_cache:
                    img_w, img_h = image_size_cache[img_name]
                else:
                    img_path = os.path.join(source_dir, img_name)
                    try:
                        with Image.open(img_path) as img:
                            img_w, img_h = img.size
                            image_size_cache[img_name] = (img_w, img_h)
                    except FileNotFoundError:
                        self.log_message(f"警告：图片 {img_name} 不存在，跳过该标注。")
                        continue
                
                # 计算YOLO格式的归一化坐标
                x_center = (xmin + xmax) / 2 / img_w
                y_center = (ymin + ymax) / 2 / img_h
                width = (xmax - xmin) / img_w
                height = (ymax - ymin) / img_h
                
                # 确保坐标在[0,1]范围内
                x_center = max(0, min(1, x_center))
                y_center = max(0, min(1, y_center))
                width = max(0, min(1, width))
                height = max(0, min(1, height))
                
                # 获取类别ID
                class_id = label_to_id[label]
                
                # 写入YOLO标注文件
                txt_name = os.path.splitext(img_name)[0] + '.txt'
                txt_path = os.path.join(output_dir, txt_name)
                
                with open(txt_path, 'a') as f:
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
                
                # 更新进度
                processed_count += 1
                progress = (processed_count / total_rows) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                
                # 每处理100条记录更新一次状态
                if processed_count % 100 == 0 or processed_count == total_rows:
                    self.root.after(0, lambda c=processed_count, t=total_rows: 
                                   self.status_var.set(f"已处理: {c}/{t}"))
            
            # 完成
            self.log_message(f"转换完成！共处理 {processed_count} 条标注记录")
            self.log_message(f"YOLO数据集已保存至 {output_dir}")
            self.status_var.set("转换完成")
            self.progress_var.set(100)
            messagebox.showinfo("完成", f"转换完成！\nYOLO数据集已保存至:\n{output_dir}")
            
        except Exception as e:
            self.log_message(f"转换过程出错: {str(e)}")
            self.status_var.set("转换失败")
            messagebox.showerror("错误", f"转换过程中发生错误:\n{str(e)}")
        
        finally:
            self.convert_button.config(state=tk.NORMAL)
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.root.after(0, lambda: self.output_text.insert(tk.END, message + "\n"))
        self.root.after(0, lambda: self.output_text.see(tk.END))

def main():
    root = tk.Tk()
    app = CSV2YOLOConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()